import React from 'react';
import { PhotoIcon, StarIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface Product {
  id: number;
  name: string;
  price: number;
  sale_price?: number;
  primary_image?: string;
  category_name?: string;
  relevance_score?: number;
  rating?: number;
  review_count?: number;
}

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
  showRelevanceScore?: boolean;
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  onClick, 
  showRelevanceScore = false,
  className = ""
}) => {
  const formatPrice = (price: number, salePrice?: number) => {
    const formatNumber = (num: number) => new Intl.NumberFormat('vi-VN').format(num);
    
    if (salePrice && salePrice < price) {
      const discount = Math.round(((price - salePrice) / price) * 100);
      return (
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <span className="text-red-600 font-bold text-lg">{formatNumber(salePrice)}₫</span>
            <span className="text-gray-500 line-through text-sm">{formatNumber(price)}₫</span>
          </div>
          <span className="inline-block bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">
            -{discount}%
          </span>
        </div>
      );
    }
    
    return <span className="text-gray-900 font-bold text-lg">{formatNumber(price)}₫</span>;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-4 w-4 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarIconSolid className="h-4 w-4 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return stars;
  };

  const handleClick = () => {
    if (onClick) {
      onClick(product);
    } else {
      window.open(`/products/${product.id}`, '_blank');
    }
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement add to cart functionality
    console.log('Add to cart:', product.id);
  };

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group ${className}`}
      onClick={handleClick}
    >
      {/* Product Image */}
      <div className="relative aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
        {product.primary_image ? (
          <img
            src={product.primary_image}
            alt={product.name}
            className="w-full h-48 object-cover object-center group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
            <PhotoIcon className="h-12 w-12 text-gray-400" />
          </div>
        )}

        {/* Relevance Score Badge */}
        {showRelevanceScore && product.relevance_score && (
          <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
            {(product.relevance_score * 100).toFixed(0)}% match
          </div>
        )}

        {/* Sale Badge */}
        {product.sale_price && product.sale_price < product.price && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
            SALE
          </div>
        )}

        {/* Quick Add to Cart Button */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={handleAddToCart}
            className="bg-white text-gray-800 p-2 rounded-full shadow-lg hover:bg-gray-50 transition-colors"
            title="Add to cart"
          >
            <ShoppingCartIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Category */}
        {product.category_name && (
          <p className="text-xs text-gray-500 mb-2 uppercase tracking-wide">
            {product.category_name}
          </p>
        )}

        {/* Product Name */}
        <h4 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
          {product.name}
        </h4>

        {/* Rating */}
        {product.rating && (
          <div className="flex items-center mb-2">
            <div className="flex items-center">
              {renderStars(product.rating)}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              {product.rating.toFixed(1)}
            </span>
            {product.review_count && (
              <span className="ml-1 text-sm text-gray-500">
                ({product.review_count})
              </span>
            )}
          </div>
        )}

        {/* Price */}
        <div className="mt-auto">
          {formatPrice(product.price, product.sale_price)}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
