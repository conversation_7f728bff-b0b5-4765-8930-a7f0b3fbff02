@echo off
echo 🏠 Furniture Store - Complete Setup Script
echo =============================================

echo.
echo 📋 This script will:
echo   1. Install all dependencies (Node.js services + Python AI service)
echo   2. Create .env file from template
echo   3. Download YOLO model for AI detection
echo   4. Start the entire application
echo.

set /p continue="Continue? (Y/N): "
if /i not "%continue%"=="Y" (
    echo Setup cancelled.
    pause
    exit /b 0
)

echo.
echo 🔍 Checking prerequisites...

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available. Please install Node.js which includes npm.
    pause
    exit /b 1
)

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.11+ from https://python.org/
    pause
    exit /b 1
)

REM Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop from https://docker.com/
    pause
    exit /b 1
)

echo ✅ All prerequisites found!

echo.
echo 📦 Step 1: Installing all dependencies...
call install-all.bat
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 📝 Step 2: Creating environment file...
if not exist .env (
    copy .env.example .env >nul
    echo ✅ .env file created from template
) else (
    echo ℹ️  .env file already exists
)

echo.
echo 📁 Step 3: Creating necessary directories...
if not exist ai-service\uploads mkdir ai-service\uploads >nul 2>&1
if not exist ai-service\models mkdir ai-service\models >nul 2>&1
if not exist logs mkdir logs >nul 2>&1
echo ✅ Directories created

echo.
echo 📥 Step 4: Downloading YOLO model...
if not exist ai-service\models\yolov8n.pt (
    echo Downloading YOLO model (~12MB)...
    curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt" -o ai-service\models\yolov8n.pt
    if %errorlevel% neq 0 (
        echo ❌ Failed to download YOLO model
        pause
        exit /b 1
    )
    echo ✅ YOLO model downloaded successfully
) else (
    echo ℹ️  YOLO model already exists
)

echo.
echo 🚀 Step 5: Starting the application...
call start.bat

echo.
echo 🎉 Setup completed successfully!
echo.
echo 💡 Useful commands for daily use:
echo    npm start          - Start the application
echo    npm run docker:down - Stop the application  
echo    npm run docker:logs - View application logs
echo    stop.bat          - Stop the application (Windows)
echo.
pause
