{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for furniture store microservices", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "http-proxy-middleware": "^2.0.6", "rate-limiter-flexible": "^3.0.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["api-gateway", "microservices", "furniture"], "author": "Your Name", "license": "MIT"}