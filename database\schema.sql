-- Database schema cho hệ thống bán nội thất
-- Database: furniture_store

-- Note: Database creation is handled by <PERSON><PERSON> Compose
-- This script assumes we're already connected to the furniture_store database

-- Bảng users
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name VA<PERSON><PERSON><PERSON>(50),
    phone VARCHAR(20),
    address TEXT,
    role VARCHAR(20) DEFAULT 'customer',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng categories
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES categories(id),
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng products
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    category_id INTEGER REFERENCES categories(id),
    brand VARCHAR(100),
    material VARCHAR(100),
    dimensions TEXT,
    weight DECIMAL(8,2),
    stock_quantity INTEGER DEFAULT 0,
    min_stock_level INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng product_images
CREATE TABLE product_images (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    image_url VARCHAR(255) NOT NULL,
    alt_text VARCHAR(200),
    is_primary BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng orders
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_address TEXT,
    billing_address TEXT,
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending',
    shipping_method VARCHAR(50),
    shipping_cost DECIMAL(8,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng order_items
CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng cart
CREATE TABLE cart (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng detection_history
CREATE TABLE detection_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    image_url VARCHAR(255),
    detection_result JSONB,
    detected_objects_count INTEGER,
    processing_time DECIMAL(8,3),
    method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng product_recommendations (enhanced)
CREATE TABLE product_recommendations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    product_id INTEGER REFERENCES products(id),
    detected_object_class VARCHAR(100),
    confidence_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4),
    recommendation_reason TEXT,
    recommendation_method VARCHAR(50) DEFAULT 'ai_detection',
    is_viewed BOOLEAN DEFAULT false,
    is_clicked BOOLEAN DEFAULT false,
    is_purchased BOOLEAN DEFAULT false,
    detection_session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng ai_detection_sessions
CREATE TABLE ai_detection_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id),
    image_url VARCHAR(255),
    image_size_width INTEGER,
    image_size_height INTEGER,
    detection_method VARCHAR(50),
    model_used VARCHAR(100),
    total_objects_detected INTEGER DEFAULT 0,
    processing_time DECIMAL(8,3),
    confidence_threshold DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng detected_objects
CREATE TABLE detected_objects (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) REFERENCES ai_detection_sessions(session_id),
    object_class VARCHAR(100) NOT NULL,
    confidence DECIMAL(5,4) NOT NULL,
    bbox_x INTEGER NOT NULL,
    bbox_y INTEGER NOT NULL,
    bbox_width INTEGER NOT NULL,
    bbox_height INTEGER NOT NULL,
    object_area INTEGER,
    is_selected BOOLEAN DEFAULT false,
    furniture_category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng ai_search_queries
CREATE TABLE ai_search_queries (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) REFERENCES ai_detection_sessions(session_id),
    user_id INTEGER REFERENCES users(id),
    search_type VARCHAR(50), -- 'single_object', 'multiple_objects', 'category_based'
    detected_objects_count INTEGER,
    search_query TEXT,
    results_count INTEGER DEFAULT 0,
    avg_relevance_score DECIMAL(5,4),
    search_duration_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng user_ai_interactions
CREATE TABLE user_ai_interactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_id VARCHAR(100) REFERENCES ai_detection_sessions(session_id),
    interaction_type VARCHAR(50), -- 'object_click', 'product_view', 'product_click', 'search_trigger'
    object_class VARCHAR(100),
    product_id INTEGER REFERENCES products(id),
    interaction_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng ai_model_performance
CREATE TABLE ai_model_performance (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    detection_method VARCHAR(50),
    total_detections INTEGER DEFAULT 0,
    avg_confidence DECIMAL(5,4),
    avg_processing_time DECIMAL(8,3),
    success_rate DECIMAL(5,4),
    furniture_objects_detected INTEGER DEFAULT 0,
    small_objects_detected INTEGER DEFAULT 0,
    date_recorded DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng object_product_mappings
CREATE TABLE object_product_mappings (
    id SERIAL PRIMARY KEY,
    object_class VARCHAR(100) NOT NULL,
    product_category_id INTEGER REFERENCES categories(id),
    mapping_weight DECIMAL(3,2) DEFAULT 1.0,
    keywords TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes để tối ưu performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_cart_user ON cart(user_id);
CREATE INDEX idx_detection_history_user ON detection_history(user_id);
CREATE INDEX idx_product_recommendations_user ON product_recommendations(user_id);

-- AI-related indexes
CREATE INDEX idx_ai_detection_sessions_user ON ai_detection_sessions(user_id);
CREATE INDEX idx_ai_detection_sessions_session_id ON ai_detection_sessions(session_id);
CREATE INDEX idx_ai_detection_sessions_created_at ON ai_detection_sessions(created_at);
CREATE INDEX idx_detected_objects_session ON detected_objects(session_id);
CREATE INDEX idx_detected_objects_class ON detected_objects(object_class);
CREATE INDEX idx_detected_objects_confidence ON detected_objects(confidence);
CREATE INDEX idx_ai_search_queries_session ON ai_search_queries(session_id);
CREATE INDEX idx_ai_search_queries_user ON ai_search_queries(user_id);
CREATE INDEX idx_ai_search_queries_type ON ai_search_queries(search_type);
CREATE INDEX idx_user_ai_interactions_user ON user_ai_interactions(user_id);
CREATE INDEX idx_user_ai_interactions_session ON user_ai_interactions(session_id);
CREATE INDEX idx_user_ai_interactions_type ON user_ai_interactions(interaction_type);
CREATE INDEX idx_ai_model_performance_model ON ai_model_performance(model_name);
CREATE INDEX idx_ai_model_performance_date ON ai_model_performance(date_recorded);
CREATE INDEX idx_object_product_mappings_class ON object_product_mappings(object_class);
CREATE INDEX idx_object_product_mappings_category ON object_product_mappings(product_category_id);
CREATE INDEX idx_product_recommendations_session ON product_recommendations(detection_session_id);
CREATE INDEX idx_product_recommendations_object_class ON product_recommendations(detected_object_class);

-- Triggers để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_object_product_mappings_updated_at BEFORE UPDATE ON object_product_mappings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function để tự động cập nhật total_objects_detected trong ai_detection_sessions
CREATE OR REPLACE FUNCTION update_session_object_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE ai_detection_sessions
        SET total_objects_detected = (
            SELECT COUNT(*)
            FROM detected_objects
            WHERE session_id = NEW.session_id
        )
        WHERE session_id = NEW.session_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE ai_detection_sessions
        SET total_objects_detected = (
            SELECT COUNT(*)
            FROM detected_objects
            WHERE session_id = OLD.session_id
        )
        WHERE session_id = OLD.session_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_session_object_count
    AFTER INSERT OR DELETE ON detected_objects
    FOR EACH ROW EXECUTE FUNCTION update_session_object_count();

-- Function để tự động tính object_area khi insert detected_objects
CREATE OR REPLACE FUNCTION calculate_object_area()
RETURNS TRIGGER AS $$
BEGIN
    NEW.object_area = NEW.bbox_width * NEW.bbox_height;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_calculate_object_area
    BEFORE INSERT OR UPDATE ON detected_objects
    FOR EACH ROW EXECUTE FUNCTION calculate_object_area();

-- Function để log user interactions
CREATE OR REPLACE FUNCTION log_ai_interaction()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.is_clicked = false AND NEW.is_clicked = true THEN
        INSERT INTO user_ai_interactions (
            user_id,
            session_id,
            interaction_type,
            object_class,
            product_id,
            interaction_data
        ) VALUES (
            NEW.user_id,
            NEW.detection_session_id,
            'product_click',
            NEW.detected_object_class,
            NEW.product_id,
            jsonb_build_object(
                'confidence_score', NEW.confidence_score,
                'relevance_score', NEW.relevance_score,
                'recommendation_method', NEW.recommendation_method
            )
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_log_recommendation_click
    AFTER UPDATE ON product_recommendations
    FOR EACH ROW EXECUTE FUNCTION log_ai_interaction();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_updated_at BEFORE UPDATE ON cart
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
