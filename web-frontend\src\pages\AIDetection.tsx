import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { aiService, productService } from '../services/api.ts';
import toast from 'react-hot-toast';
import ProductCard from '../components/ProductCard';
import ProductSkeleton from '../components/ProductSkeleton';
import {
  CameraIcon,
  PhotoIcon,
  ArrowUpTrayIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
  ClockIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/24/outline';

interface DetectedObject {
  class: string;
  confidence: number;
  bbox: [number, number, number, number]; // [x1, y1, x2, y2]
}

interface DetectionResult {
  detected_objects: DetectedObject[];
  processing_time: number;
  timestamp: string;
  method: string;
  image_size: [number, number];
}

interface Product {
  id: number;
  name: string;
  price: number;
  sale_price?: number;
  primary_image?: string;
  category_name?: string;
  relevance_score?: number;
}

interface AISearchResult {
  products: Product[];
  categories: any[];
  searchQueries: any[];
  detectedObjectsCount: number;
  totalConfidence: number;
}

const AIDetection: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [detectionResult, setDetectionResult] = useState<DetectionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'standard' | 'slice' | 'super-slice'>('standard');
  const [selectedObject, setSelectedObject] = useState<DetectedObject | null>(null);
  const [searchResults, setSearchResults] = useState<AISearchResult | null>(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showProducts, setShowProducts] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setDetectionResult(null);
      setSelectedObject(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  const handleDetection = async () => {
    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    setLoading(true);
    try {
      let response;

      switch (selectedMethod) {
        case 'slice':
          response = await aiService.detectObjectsSlice(selectedFile);
          break;
        case 'super-slice':
          response = await aiService.detectObjectsSuperSlice(selectedFile);
          break;
        default:
          response = await aiService.detectObjects(selectedFile);
      }

      setDetectionResult(response.data);
      toast.success(`Detected ${response.data.detected_objects.length} objects!`);

      // Draw bounding boxes after a short delay to ensure image is loaded
      setTimeout(() => drawBoundingBoxes(response.data), 100);

    } catch (error: any) {
      console.error('Detection error:', error);
      toast.error(error.response?.data?.detail || 'Detection failed');
    } finally {
      setLoading(false);
    }
  };

  const drawBoundingBoxes = (result: DetectionResult) => {
    const canvas = canvasRef.current;
    const image = imageRef.current;

    if (!canvas || !image) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match image display size
    const rect = image.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;
    canvas.style.width = `${rect.width}px`;
    canvas.style.height = `${rect.height}px`;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate scale factors
    const scaleX = rect.width / result.image_size[0];
    const scaleY = rect.height / result.image_size[1];

    // Draw bounding boxes
    result.detected_objects.forEach((obj, index) => {
      const [x1, y1, x2, y2] = obj.bbox;

      // Scale coordinates
      const scaledX1 = x1 * scaleX;
      const scaledY1 = y1 * scaleY;
      const scaledX2 = x2 * scaleX;
      const scaledY2 = y2 * scaleY;

      const width = scaledX2 - scaledX1;
      const height = scaledY2 - scaledY1;

      // Set style based on selection
      const isSelected = selectedObject === obj;
      ctx.strokeStyle = isSelected ? '#ef4444' : '#10b981';
      ctx.lineWidth = isSelected ? 3 : 2;
      ctx.fillStyle = isSelected ? 'rgba(239, 68, 68, 0.1)' : 'rgba(16, 185, 129, 0.1)';

      // Draw rectangle
      ctx.fillRect(scaledX1, scaledY1, width, height);
      ctx.strokeRect(scaledX1, scaledY1, width, height);

      // Draw label
      const label = `${obj.class} (${(obj.confidence * 100).toFixed(1)}%)`;
      ctx.fillStyle = isSelected ? '#ef4444' : '#10b981';
      ctx.font = '12px Arial';

      const textWidth = ctx.measureText(label).width;
      const labelX = scaledX1;
      const labelY = scaledY1 - 5;

      // Label background
      ctx.fillRect(labelX, labelY - 15, textWidth + 8, 18);

      // Label text
      ctx.fillStyle = 'white';
      ctx.fillText(label, labelX + 4, labelY - 2);
    });
  };

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!detectionResult) return;

    const canvas = canvasRef.current;
    const image = imageRef.current;
    if (!canvas || !image) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Calculate scale factors
    const scaleX = rect.width / detectionResult.image_size[0];
    const scaleY = rect.height / detectionResult.image_size[1];

    // Find clicked object
    const clickedObject = detectionResult.detected_objects.find(obj => {
      const [x1, y1, x2, y2] = obj.bbox;
      const scaledX1 = x1 * scaleX;
      const scaledY1 = y1 * scaleY;
      const scaledX2 = x2 * scaleX;
      const scaledY2 = y2 * scaleY;

      return x >= scaledX1 && x <= scaledX2 && y >= scaledY1 && y <= scaledY2;
    });

    if (clickedObject) {
      setSelectedObject(clickedObject);
      drawBoundingBoxes(detectionResult);
      toast.success(`Selected: ${clickedObject.class}`);
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setPreviewUrl('');
    setDetectionResult(null);
    setSelectedObject(null);
    setSearchResults(null);
    setShowProducts(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const searchSimilarProducts = async () => {
    if (!selectedObject) {
      toast.error('Please select an object first');
      return;
    }

    setSearchLoading(true);
    try {
      // Use AI-powered search for better results
      const response = await productService.getRecommendationsForObject(
        selectedObject.class,
        selectedObject.confidence,
        12
      );

      setSearchResults(response.data);
      setShowProducts(true);
      toast.success(`Found ${response.data.products.length} similar products!`);
    } catch (error: any) {
      console.error('Search error:', error);
      toast.error('Failed to find similar products');
      // Fallback to traditional search
      const searchQuery = selectedObject.class;
      window.location.href = `/products?search=${encodeURIComponent(searchQuery)}`;
    } finally {
      setSearchLoading(false);
    }
  };

  const searchAllDetectedObjects = async () => {
    if (!detectionResult || detectionResult.detected_objects.length === 0) {
      toast.error('No detected objects to search for');
      return;
    }

    setSearchLoading(true);
    try {
      // Convert detected objects to the expected format
      const detectedObjects = detectionResult.detected_objects.map(obj => ({
        class_name: obj.class,
        confidence: obj.confidence,
        bounding_box: {
          x: obj.bbox[0],
          y: obj.bbox[1],
          width: obj.bbox[2] - obj.bbox[0],
          height: obj.bbox[3] - obj.bbox[1]
        }
      }));

      const response = await productService.searchByDetectedObjects(detectedObjects, {
        limit: 20,
        sort_by: 'relevance',
        min_confidence: 0.2,
        include_categories: true
      });

      setSearchResults(response.data);
      setShowProducts(true);
      toast.success(`Found ${response.data.products.length} products based on ${detectedObjects.length} detected objects!`);
    } catch (error: any) {
      console.error('Search error:', error);
      toast.error('Failed to search for products');
    } finally {
      setSearchLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI Furniture Detection
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Upload an image and let our AI identify furniture items using advanced YOLO detection techniques.
            Click on detected objects to find similar products in our store.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Upload and Controls */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Upload Image</h2>

              {/* File Upload */}
              {!selectedFile ? (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <input {...getInputProps()} />
                  <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">
                    {isDragActive
                      ? 'Drop the image here...'
                      : 'Drag & drop an image here, or click to select'}
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports: JPG, PNG, BMP, TIFF (max 10MB)
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 truncate">
                      {selectedFile.name}
                    </span>
                    <button
                      onClick={clearSelection}
                      className="text-red-500 hover:text-red-700"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>

                  {/* Detection Method Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Detection Method
                    </label>
                    <select
                      value={selectedMethod}
                      onChange={(e) => setSelectedMethod(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="standard">Standard Detection</option>
                      <option value="slice">Slicing (2x2)</option>
                      <option value="super-slice">Super Slicing (4x3)</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Slicing methods improve detection of small objects
                    </p>
                  </div>

                  {/* Detect Button */}
                  <button
                    onClick={handleDetection}
                    disabled={loading}
                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Detecting...
                      </>
                    ) : (
                      <>
                        <SparklesIcon className="h-4 w-4 mr-2" />
                        Detect Objects
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>

            {/* Detection Results */}
            {detectionResult && (
              <div className="bg-white rounded-lg shadow-md p-6 mt-6">
                <h3 className="text-lg font-semibold mb-4">Detection Results</h3>

                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    Processing time: {detectionResult.processing_time.toFixed(2)}s
                  </div>

                  <div className="text-sm text-gray-600">
                    Method: {detectionResult.method}
                  </div>

                  <div className="text-sm text-gray-600">
                    Objects found: {detectionResult.detected_objects.length}
                  </div>
                </div>

                {/* Object List */}
                <div className="mt-4 space-y-2 max-h-60 overflow-y-auto">
                  {detectionResult.detected_objects.map((obj, index) => (
                    <div
                      key={index}
                      onClick={() => {
                        setSelectedObject(obj);
                        drawBoundingBoxes(detectionResult);
                      }}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedObject === obj
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{obj.class}</span>
                        <span className="text-sm text-gray-500">
                          {(obj.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Search Products Buttons */}
                {selectedObject && (
                  <button
                    onClick={searchSimilarProducts}
                    disabled={searchLoading}
                    className="w-full mt-4 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    {searchLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Searching...
                      </>
                    ) : (
                      <>
                        <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                        Find Similar Products
                      </>
                    )}
                  </button>
                )}

                {/* Search All Objects Button */}
                {detectionResult && detectionResult.detected_objects.length > 1 && (
                  <button
                    onClick={searchAllDetectedObjects}
                    disabled={searchLoading}
                    className="w-full mt-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    {searchLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Searching...
                      </>
                    ) : (
                      <>
                        <SparklesIcon className="h-4 w-4 mr-2" />
                        Find Products for All Objects
                      </>
                    )}
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Right Panel - Image Display */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Image Preview</h2>

              {previewUrl ? (
                <div className="relative">
                  <img
                    ref={imageRef}
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-auto rounded-lg"
                    onLoad={() => {
                      if (detectionResult) {
                        drawBoundingBoxes(detectionResult);
                      }
                    }}
                  />
                  <canvas
                    ref={canvasRef}
                    onClick={handleCanvasClick}
                    className="absolute top-0 left-0 cursor-pointer"
                    style={{ pointerEvents: detectionResult ? 'auto' : 'none' }}
                  />

                  {selectedObject && (
                    <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-lg text-sm">
                      Selected: {selectedObject.class} ({(selectedObject.confidence * 100).toFixed(1)}%)
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                  <div className="text-center">
                    <CameraIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No image selected</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Search Results */}
        {showProducts && searchResults && (
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Search Results ({searchResults.products.length} products found)
              </h3>
              <button
                onClick={() => setShowProducts(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Search Summary */}
            {searchResults.detectedObjectsCount > 0 && (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  Found products based on {searchResults.detectedObjectsCount} detected objects
                  with {(searchResults.totalConfidence * 100).toFixed(1)}% average confidence
                </p>
              </div>
            )}

            {/* Products Grid */}
            {searchLoading ? (
              <ProductSkeleton count={8} />
            ) : searchResults.products.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {searchResults.products.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    showRelevanceScore={true}
                    onClick={(product) => window.open(`/products/${product.id}`, '_blank')}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <MagnifyingGlassIcon className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  We couldn't find any products matching the detected objects. Try adjusting the detection method or upload a different image.
                </p>
              </div>
            )}

            {/* Categories */}
            {searchResults.categories && searchResults.categories.length > 0 && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="text-lg font-medium text-gray-900 mb-3">Related Categories</h4>
                <div className="flex flex-wrap gap-2">
                  {searchResults.categories.map((category, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer"
                      onClick={() => window.open(`/products?category=${category.id}`, '_blank')}
                    >
                      {category.name}
                      {category.product_count && (
                        <span className="ml-1 text-xs text-gray-500">({category.product_count})</span>
                      )}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">How to use:</h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li>Upload an image containing furniture items</li>
            <li>Choose a detection method (slicing methods work better for small objects)</li>
            <li>Click "Detect Objects" to analyze the image</li>
            <li>Click on any detected object (bounding box) to select it</li>
            <li>Use "Find Similar Products" to search our catalog with AI-powered matching</li>
            <li>Click "Find Products for All Objects" to search based on all detected items</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AIDetection;
