import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from pathlib import Path
import logging
from typing import List, Dict, Any, Optional, Tuple
import asyncio
import yaml
import json
import time
from datetime import datetime

from models.detection_result import DetectionResult, BoundingBox, DetectedObject

logger = logging.getLogger(__name__)

class EnhancedObjectDetectionService:
    """
    Enhanced Object Detection Service with fine-tuned furniture models
    Supports multiple models, confidence tuning, and optimized detection
    """
    
    def __init__(self, config_path: str = "detection_config.yaml"):
        self.config = self.load_config(config_path)
        self.models = {}
        self.current_model_name = "default"
        self.furniture_classes = self.load_furniture_classes()
        self.class_confidence_thresholds = self.config.get('class_confidence_thresholds', {})
        self.load_models()
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load detection configuration"""
        default_config = {
            'models': {
                'default': {
                    'path': 'models/yolo11s-seg.pt',
                    'type': 'segmentation',
                    'confidence': 0.25,
                    'iou': 0.45
                },
                'furniture_finetuned': {
                    'path': 'models/furniture_yolo11s_best.pt',
                    'type': 'detection',
                    'confidence': 0.3,
                    'iou': 0.5
                },
                'small_objects': {
                    'path': 'models/small_objects_yolo11s.pt',
                    'type': 'detection',
                    'confidence': 0.2,
                    'iou': 0.4
                }
            },
            'detection_settings': {
                'max_det': 300,
                'agnostic_nms': False,
                'retina_masks': True,
                'half': False,
                'device': 'auto'
            },
            'class_confidence_thresholds': {
                'cup': 0.15,
                'plate': 0.15,
                'bowl': 0.15,
                'pillow': 0.2,
                'book': 0.2,
                'clock': 0.25,
                'vase': 0.25,
                'chair': 0.3,
                'table': 0.3,
                'sofa': 0.35,
                'bed': 0.35
            },
            'small_object_enhancement': {
                'enabled': True,
                'min_size': 32,
                'scale_factors': [0.8, 1.0, 1.2],
                'tta': True  # Test Time Augmentation
            }
        }
        
        if Path(config_path).exists():
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                # Deep merge configs
                self._deep_merge(default_config, user_config)
        
        return default_config
    
    def _deep_merge(self, base_dict: Dict, update_dict: Dict) -> None:
        """Deep merge two dictionaries"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def load_furniture_classes(self) -> Dict[str, str]:
        """Load furniture class mappings"""
        furniture_classes = {
            'chair': 'Chairs and Seating',
            'table': 'Tables',
            'sofa': 'Sofas and Couches', 
            'bed': 'Beds',
            'desk': 'Desks',
            'cabinet': 'Cabinets and Storage',
            'shelf': 'Shelves',
            'lamp': 'Lighting',
            'pillow': 'Pillows and Cushions',
            'plate': 'Dinnerware',
            'cup': 'Drinkware',
            'bowl': 'Bowls',
            'vase': 'Decorative Items',
            'mirror': 'Mirrors',
            'curtain': 'Window Treatments',
            'rug': 'Floor Coverings',
            'plant': 'Plants and Greenery',
            'book': 'Books and Media',
            'clock': 'Clocks',
            'picture': 'Wall Art'
        }
        return furniture_classes
    
    def load_models(self):
        """Load all configured models"""
        for model_name, model_config in self.config['models'].items():
            try:
                model_path = model_config['path']
                if Path(model_path).exists():
                    self.models[model_name] = {
                        'model': YOLO(model_path),
                        'config': model_config
                    }
                    logger.info(f"Loaded model '{model_name}' from {model_path}")
                else:
                    logger.warning(f"Model file not found: {model_path}")
            except Exception as e:
                logger.error(f"Error loading model '{model_name}': {e}")
        
        # Fallback to default model if no models loaded
        if not self.models:
            try:
                self.models['fallback'] = {
                    'model': YOLO('yolo11s.pt'),
                    'config': self.config['models']['default']
                }
                logger.info("Loaded fallback YOLO model")
            except Exception as e:
                logger.error(f"Failed to load fallback model: {e}")
                raise
    
    def set_model(self, model_name: str) -> bool:
        """Switch to a specific model"""
        if model_name in self.models:
            self.current_model_name = model_name
            logger.info(f"Switched to model: {model_name}")
            return True
        else:
            logger.warning(f"Model '{model_name}' not available")
            return False
    
    def get_current_model(self) -> Tuple[YOLO, Dict[str, Any]]:
        """Get current active model and its config"""
        if self.current_model_name in self.models:
            model_info = self.models[self.current_model_name]
            return model_info['model'], model_info['config']
        else:
            # Return first available model
            first_model = next(iter(self.models.values()))
            return first_model['model'], first_model['config']
    
    async def detect_objects(self, image_path: str, model_name: Optional[str] = None) -> DetectionResult:
        """
        Enhanced object detection with confidence tuning and furniture focus
        """
        try:
            # Switch model if specified
            if model_name and model_name in self.models:
                original_model = self.current_model_name
                self.set_model(model_name)
            else:
                original_model = None
            
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Cannot read image")
            
            # Get current model and config
            model, model_config = self.get_current_model()
            
            # Run inference with model-specific settings
            results = model(
                image,
                conf=model_config.get('confidence', 0.25),
                iou=model_config.get('iou', 0.45),
                max_det=self.config['detection_settings']['max_det'],
                agnostic_nms=self.config['detection_settings']['agnostic_nms'],
                retina_masks=self.config['detection_settings']['retina_masks'],
                half=self.config['detection_settings']['half'],
                device=self.config['detection_settings']['device']
            )
            
            # Process results with enhanced filtering
            detected_objects = self._process_detection_results(results, image.shape)
            
            # Apply furniture-specific post-processing
            detected_objects = self._apply_furniture_filtering(detected_objects)
            
            # Restore original model if switched
            if original_model:
                self.set_model(original_model)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=detected_objects,
                total_objects=len(detected_objects),
                method=f"enhanced_{self.current_model_name}",
                image_size=(image.shape[1], image.shape[0])
            )
            
        except Exception as e:
            logger.error(f"Error in enhanced object detection: {e}")
            raise
    
    def _process_detection_results(self, results, image_shape: Tuple[int, int, int]) -> List[DetectedObject]:
        """Process YOLO detection results with enhanced filtering"""
        detected_objects = []
        
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Extract detection data
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    # Get class name
                    if hasattr(result, 'names') and class_id in result.names:
                        class_name = result.names[class_id]
                    else:
                        class_name = f"class_{class_id}"
                    
                    # Apply class-specific confidence thresholds
                    min_confidence = self.class_confidence_thresholds.get(class_name, 0.25)
                    if confidence < min_confidence:
                        continue
                    
                    # Calculate object size for small object enhancement
                    obj_width = x2 - x1
                    obj_height = y2 - y1
                    obj_area = obj_width * obj_height
                    
                    # Skip very small detections that might be noise
                    min_area = self.config['small_object_enhancement']['min_size'] ** 2
                    if obj_area < min_area:
                        continue
                    
                    # Create bounding box
                    bbox = BoundingBox(
                        x=int(x1),
                        y=int(y1),
                        width=int(obj_width),
                        height=int(obj_height)
                    )
                    
                    # Create detected object with enhanced metadata
                    obj = DetectedObject(
                        class_name=class_name,
                        class_id=class_id,
                        confidence=confidence,
                        bounding_box=bbox
                    )
                    
                    # Add furniture category if available
                    if class_name in self.furniture_classes:
                        obj.furniture_category = self.furniture_classes[class_name]
                    
                    detected_objects.append(obj)
        
        return detected_objects

    def _apply_furniture_filtering(self, detected_objects: List[DetectedObject]) -> List[DetectedObject]:
        """Apply furniture-specific filtering and enhancement"""
        # Filter to keep only furniture-related objects
        furniture_objects = []

        for obj in detected_objects:
            # Keep objects that are in our furniture classes
            if obj.class_name in self.furniture_classes:
                furniture_objects.append(obj)
            # Also keep some general objects that might be furniture-related
            elif obj.class_name in ['person', 'bottle', 'book', 'clock', 'vase']:
                furniture_objects.append(obj)

        # Sort by confidence (highest first)
        furniture_objects.sort(key=lambda x: x.confidence, reverse=True)

        return furniture_objects

    async def detect_objects_slice(self, image_path: str, model_name: Optional[str] = None) -> DetectionResult:
        """
        Enhanced slicing detection optimized for small furniture objects
        Uses adaptive slicing based on image size and object types
        """
        try:
            # Switch model if specified
            if model_name and model_name in self.models:
                original_model = self.current_model_name
                self.set_model(model_name)
            else:
                original_model = None

            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Cannot read image")

            height, width = image.shape[:2]

            # Get current model and config
            model, model_config = self.get_current_model()

            # Adaptive slicing parameters based on image size
            if width * height > 1920 * 1080:  # Large images
                overlap = 0.3
                grid_size = (3, 3)
            else:  # Standard images
                overlap = 0.2
                grid_size = (2, 2)

            all_detections = []

            # Generate slice coordinates
            slices = self._generate_slice_coordinates(width, height, grid_size, overlap)

            for i, (x, y, w, h) in enumerate(slices):
                # Ensure slice boundaries are within image
                x = max(0, min(x, width - 1))
                y = max(0, min(y, height - 1))
                w = min(w, width - x)
                h = min(h, height - y)

                if w <= 0 or h <= 0:
                    continue

                # Extract slice
                slice_img = image[y:y+h, x:x+w]

                # Run detection on slice with enhanced settings
                results = model(
                    slice_img,
                    conf=max(0.15, model_config.get('confidence', 0.25) - 0.1),  # Lower confidence for slices
                    iou=model_config.get('iou', 0.45),
                    max_det=self.config['detection_settings']['max_det'],
                    agnostic_nms=False,
                    device=self.config['detection_settings']['device']
                )

                # Process slice results
                slice_detections = self._process_slice_results(results, x, y, slice_img.shape)
                all_detections.extend(slice_detections)

            # Apply enhanced NMS and filtering
            filtered_detections = self._apply_furniture_filtering(all_detections)
            filtered_detections = self._furniture_nms(filtered_detections, iou_threshold=0.4)

            # Restore original model if switched
            if original_model:
                self.set_model(original_model)

            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method=f"enhanced_slicing_{grid_size[0]}x{grid_size[1]}",
                image_size=(width, height)
            )

        except Exception as e:
            logger.error(f"Error in enhanced slice detection: {e}")
            raise

    def _generate_slice_coordinates(self, width: int, height: int, grid_size: Tuple[int, int], overlap: float) -> List[Tuple[int, int, int, int]]:
        """Generate optimized slice coordinates"""
        rows, cols = grid_size
        slice_width = int(width * (1 + overlap) / cols)
        slice_height = int(height * (1 + overlap) / rows)

        slices = []
        for row in range(rows):
            for col in range(cols):
                x = int(col * width / cols - slice_width * overlap / 2)
                y = int(row * height / rows - slice_height * overlap / 2)

                # Ensure coordinates are valid
                x = max(0, x)
                y = max(0, y)

                slices.append((x, y, slice_width, slice_height))

        return slices

    def _process_slice_results(self, results, offset_x: int, offset_y: int, slice_shape: Tuple[int, int, int]) -> List[DetectedObject]:
        """Process detection results from a single slice"""
        detected_objects = []

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())

                    # Get class name
                    if hasattr(result, 'names') and class_id in result.names:
                        class_name = result.names[class_id]
                    else:
                        class_name = f"class_{class_id}"

                    # Apply class-specific confidence thresholds
                    min_confidence = self.class_confidence_thresholds.get(class_name, 0.15)
                    if confidence < min_confidence:
                        continue

                    # Adjust coordinates to global image space
                    global_x1 = int(x1 + offset_x)
                    global_y1 = int(y1 + offset_y)
                    global_x2 = int(x2 + offset_x)
                    global_y2 = int(y2 + offset_y)

                    # Create bounding box
                    bbox = BoundingBox(
                        x=global_x1,
                        y=global_y1,
                        width=global_x2 - global_x1,
                        height=global_y2 - global_y1
                    )

                    # Create detected object
                    obj = DetectedObject(
                        class_name=class_name,
                        class_id=class_id,
                        confidence=confidence,
                        bounding_box=bbox
                    )

                    # Add furniture category if available
                    if class_name in self.furniture_classes:
                        obj.furniture_category = self.furniture_classes[class_name]

                    detected_objects.append(obj)

        return detected_objects

    def _furniture_nms(self, objects: List[DetectedObject], iou_threshold: float = 0.5) -> List[DetectedObject]:
        """Furniture-specific Non-Maximum Suppression"""
        if not objects:
            return []

        # Group objects by class for class-specific NMS
        class_groups = {}
        for obj in objects:
            if obj.class_name not in class_groups:
                class_groups[obj.class_name] = []
            class_groups[obj.class_name].append(obj)

        filtered_objects = []

        for class_name, class_objects in class_groups.items():
            # Apply NMS within each class
            class_filtered = self._non_max_suppression(class_objects, iou_threshold)
            filtered_objects.extend(class_filtered)

        return filtered_objects

    def _non_max_suppression(self, detections: List[DetectedObject], iou_threshold: float = 0.5) -> List[DetectedObject]:
        """
        Non-Maximum Suppression for duplicate detection removal
        """
        if not detections:
            return []

        # Sort by confidence (highest first)
        detections.sort(key=lambda x: x.confidence, reverse=True)

        filtered = []

        for detection in detections:
            is_duplicate = False

            for filtered_detection in filtered:
                if self._calculate_iou(detection.bounding_box, filtered_detection.bounding_box) > iou_threshold:
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered.append(detection)

        return filtered

    def _calculate_iou(self, box1: BoundingBox, box2: BoundingBox) -> float:
        """
        Calculate Intersection over Union (IoU) between two bounding boxes
        """
        # Calculate intersection coordinates
        x1 = max(box1.x, box2.x)
        y1 = max(box1.y, box2.y)
        x2 = min(box1.x + box1.width, box2.x + box2.width)
        y2 = min(box1.y + box1.height, box2.y + box2.height)

        # Calculate intersection area
        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # Calculate union area
        area1 = box1.width * box1.height
        area2 = box2.width * box2.height
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    async def detect_objects_super_slice(self, image_path: str, model_name: Optional[str] = None) -> DetectionResult:
        """
        Enhanced super slicing detection for maximum small object detection
        Uses 4x3 grid with optimized overlap and multi-scale processing
        """
        try:
            # Switch model if specified
            if model_name and model_name in self.models:
                original_model = self.current_model_name
                self.set_model(model_name)
            else:
                original_model = None

            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Cannot read image")

            height, width = image.shape[:2]

            # Get current model and config
            model, model_config = self.get_current_model()

            # Super slicing with 4x3 grid and higher overlap
            overlap = 0.4  # Higher overlap for better small object detection
            grid_size = (4, 3)

            all_detections = []

            # Generate slice coordinates
            slices = self._generate_slice_coordinates(width, height, grid_size, overlap)

            # Process each slice
            for i, (x, y, w, h) in enumerate(slices):
                # Ensure slice boundaries are within image
                x = max(0, min(x, width - 1))
                y = max(0, min(y, height - 1))
                w = min(w, width - x)
                h = min(h, height - y)

                if w <= 0 or h <= 0:
                    continue

                # Extract slice
                slice_img = image[y:y+h, x:x+w]

                # Multi-scale detection for small objects
                if self.config['small_object_enhancement']['enabled']:
                    scale_factors = self.config['small_object_enhancement']['scale_factors']
                    for scale in scale_factors:
                        scaled_slice = self._scale_image(slice_img, scale)

                        # Run detection on scaled slice
                        results = model(
                            scaled_slice,
                            conf=max(0.1, model_config.get('confidence', 0.25) - 0.15),  # Very low confidence for super slicing
                            iou=model_config.get('iou', 0.45),
                            max_det=self.config['detection_settings']['max_det'],
                            agnostic_nms=False,
                            device=self.config['detection_settings']['device']
                        )

                        # Process scaled slice results
                        slice_detections = self._process_scaled_slice_results(results, x, y, scale, slice_img.shape)
                        all_detections.extend(slice_detections)
                else:
                    # Standard slice processing
                    results = model(
                        slice_img,
                        conf=max(0.1, model_config.get('confidence', 0.25) - 0.15),
                        iou=model_config.get('iou', 0.45),
                        max_det=self.config['detection_settings']['max_det'],
                        agnostic_nms=False,
                        device=self.config['detection_settings']['device']
                    )

                    slice_detections = self._process_slice_results(results, x, y, slice_img.shape)
                    all_detections.extend(slice_detections)

            # Apply aggressive filtering and NMS for super slicing
            filtered_detections = self._apply_furniture_filtering(all_detections)
            filtered_detections = self._furniture_nms(filtered_detections, iou_threshold=0.3)  # Lower IoU threshold

            # Restore original model if switched
            if original_model:
                self.set_model(original_model)

            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method=f"enhanced_super_slicing_{grid_size[0]}x{grid_size[1]}",
                image_size=(width, height)
            )

        except Exception as e:
            logger.error(f"Error in enhanced super slice detection: {e}")
            raise

    def _scale_image(self, image: np.ndarray, scale_factor: float) -> np.ndarray:
        """Scale image for multi-scale detection"""
        if scale_factor == 1.0:
            return image

        height, width = image.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LINEAR)

    def _process_scaled_slice_results(self, results, offset_x: int, offset_y: int, scale_factor: float, original_shape: Tuple[int, int, int]) -> List[DetectedObject]:
        """Process detection results from a scaled slice"""
        detected_objects = []

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())

                    # Get class name
                    if hasattr(result, 'names') and class_id in result.names:
                        class_name = result.names[class_id]
                    else:
                        class_name = f"class_{class_id}"

                    # Apply class-specific confidence thresholds
                    min_confidence = self.class_confidence_thresholds.get(class_name, 0.1)
                    if confidence < min_confidence:
                        continue

                    # Scale coordinates back to original slice size
                    x1 = x1 / scale_factor
                    y1 = y1 / scale_factor
                    x2 = x2 / scale_factor
                    y2 = y2 / scale_factor

                    # Adjust coordinates to global image space
                    global_x1 = int(x1 + offset_x)
                    global_y1 = int(y1 + offset_y)
                    global_x2 = int(x2 + offset_x)
                    global_y2 = int(y2 + offset_y)

                    # Create bounding box
                    bbox = BoundingBox(
                        x=global_x1,
                        y=global_y1,
                        width=global_x2 - global_x1,
                        height=global_y2 - global_y1
                    )

                    # Create detected object
                    obj = DetectedObject(
                        class_name=class_name,
                        class_id=class_id,
                        confidence=confidence,
                        bounding_box=bbox
                    )

                    # Add furniture category if available
                    if class_name in self.furniture_classes:
                        obj.furniture_category = self.furniture_classes[class_name]

                    detected_objects.append(obj)

        return detected_objects
