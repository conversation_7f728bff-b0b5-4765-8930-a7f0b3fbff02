import React from 'react';

interface ProductSkeletonProps {
  count?: number;
  className?: string;
}

const ProductSkeleton: React.FC<ProductSkeletonProps> = ({ count = 8, className = "" }) => {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden animate-pulse">
          {/* Image Skeleton */}
          <div className="w-full h-48 bg-gray-300"></div>
          
          {/* Content Skeleton */}
          <div className="p-4 space-y-3">
            {/* Category */}
            <div className="h-3 bg-gray-300 rounded w-1/3"></div>
            
            {/* Title */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-full"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
            
            {/* Rating */}
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="h-4 w-4 bg-gray-300 rounded"></div>
                ))}
              </div>
              <div className="h-3 bg-gray-300 rounded w-8"></div>
            </div>
            
            {/* Price */}
            <div className="space-y-1">
              <div className="h-5 bg-gray-300 rounded w-1/2"></div>
              <div className="h-3 bg-gray-300 rounded w-1/4"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductSkeleton;
