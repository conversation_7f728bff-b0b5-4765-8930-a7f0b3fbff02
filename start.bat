@echo off
echo 🏠 Starting Furniture Store with AI Detection...

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    echo    Download Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ docker-compose is not available. Please install Docker Desktop which includes docker-compose.
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file from .env.example...
    copy .env.example .env >nul
    echo ⚠️  .env file created. You can modify it if needed.
    echo.
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist ai-service\uploads mkdir ai-service\uploads >nul 2>&1
if not exist ai-service\models mkdir ai-service\models >nul 2>&1
if not exist logs mkdir logs >nul 2>&1

REM Download YOLO model if it doesn't exist
if not exist ai-service\models\yolov8n.pt (
    echo 📥 Downloading YOLO model...
    echo This may take a few minutes...
    curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt" -o ai-service\models\yolov8n.pt
    if %errorlevel% neq 0 (
        echo ❌ Failed to download YOLO model. Please check your internet connection.
        pause
        exit /b 1
    )
)

echo.
echo 🔨 Building and starting services with Docker Compose...
echo This will take a few minutes on first run...
docker-compose up --build -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start services. Please check Docker logs.
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo.
echo 🌐 Services should now be available at:
echo   - Frontend:        http://localhost:3000
echo   - API Gateway:     http://localhost:8000
echo   - AI Service:      http://localhost:8001
echo   - Product Service: http://localhost:8002
echo   - User Service:    http://localhost:8003
echo   - Order Service:   http://localhost:8004
echo   - Grafana:         http://localhost:3001 (admin/admin)
echo   - RabbitMQ:        http://localhost:15672 (admin/password123)
echo   - Prometheus:      http://localhost:9090
echo.
echo ✅ Furniture Store started successfully!
echo.
echo 📋 Useful commands:
echo   - View logs:       docker-compose logs -f
echo   - Stop services:   docker-compose down
echo   - Restart:         docker-compose restart
echo   - Check status:    docker-compose ps
echo.
pause
