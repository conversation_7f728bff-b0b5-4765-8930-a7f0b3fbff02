{"name": "product-service", "version": "1.0.0", "description": "Product management service for furniture store", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "pg": "^8.11.3", "redis": "^4.6.10", "joi": "^17.11.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["furniture", "product", "microservice"], "author": "Your Name", "license": "MIT"}