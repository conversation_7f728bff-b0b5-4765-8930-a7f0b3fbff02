from ultralytics import YOLO

def main():
    print("Bắt đầu quá trình tải dataset HomeObjects-3K và huấn luyện thử nghiệm...")
    
    # 1. Tải một mô hình YOLOv8 nhỏ đã được huấn luyện trước
    model = YOLO('yolov8n.pt')
    results = model.train(
        data='HomeObjects-3K.yaml', 
        epochs=3, 
        imgsz=640,
        project='runs', # Lưu kết quả vào thư mục 'runs'
        name='baseline_test' 
    )

    print("\nHoàn tất! Đã tải xong dataset và chạy thử nghiệm thành công.")
    print("Vui lòng kiểm tra thư mục 'datasets/HomeObjects-3K' để xem dữ liệu.")

if __name__ == '__main__':
    main()