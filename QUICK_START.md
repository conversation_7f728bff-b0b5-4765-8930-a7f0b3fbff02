# 🚀 Quick Reference Guide

## 📦 Installation & Setup

### First Time Setup (Complete)
```bash
# Method 1: NPM (Cross-platform)
npm run setup

# Method 2: Windows Batch (Recommended for Windows)
setup.bat
```

### Install Dependencies Only
```bash
# NPM way
npm run install:all

# Windows way  
install-all.bat
```

## 🏃‍♂️ Running the Application

### Start Full Stack (Production Mode)
```bash
# NPM way
npm start

# Windows way
start.bat
```

### Development Mode
```bash
# NPM way (individual services)
npm run dev:frontend       # React app on port 3000
npm run dev:api-gateway    # API Gateway on port 8000
npm run dev:user-service   # User service on port 8003
npm run dev:product-service # Product service on port 8002
npm run dev:order-service  # Order service on port 8004
npm run dev:ai             # AI service on port 8001

# Windows way (all services concurrently)
start-dev.bat
```

## 🛑 Stopping Services

```bash
# NPM way
npm run docker:down

# Windows way
stop.bat
```

## 📊 Monitoring & Logs

```bash
# View all logs
npm run docker:logs

# View specific service logs
npm run logs:frontend
npm run logs:api-gateway
npm run logs:ai
npm run logs:user
npm run logs:product
npm run logs:order

# Check service status
npm run status
```

## 🔧 Useful Commands

```bash
# Restart all services
npm run docker:restart

# Clean up Docker (remove containers, images)
npm run clean

# Build production
npm run build

# Run tests
npm test
npm run test:frontend
npm run test:services
```

## 🌐 Access URLs

- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8000
- **AI Service**: http://localhost:8001
- **Product Service**: http://localhost:8002
- **User Service**: http://localhost:8003
- **Order Service**: http://localhost:8004
- **Grafana**: http://localhost:3001 (admin/admin)
- **RabbitMQ**: http://localhost:15672 (admin/password123)
- **Prometheus**: http://localhost:9090

## 🐳 Docker Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild and start
docker-compose up --build -d

# Check status
docker-compose ps
```

## 🚨 Troubleshooting

### Common Issues:

1. **Port already in use**
   ```bash
   # Stop services and restart
   npm run docker:down
   npm start
   ```

2. **Docker not running**
   - Start Docker Desktop
   - Wait for it to fully load
   - Run `npm start` again

3. **Dependencies missing**
   ```bash
   npm run install:all
   ```

4. **YOLO model missing**
   ```bash
   npm run download-model
   ```

5. **Clean start**
   ```bash
   npm run clean
   npm run setup
   ```

### Windows Specific:
- Use `.bat` files for better Windows compatibility
- Make sure Docker Desktop is running
- Check Windows Defender/Antivirus isn't blocking Docker
