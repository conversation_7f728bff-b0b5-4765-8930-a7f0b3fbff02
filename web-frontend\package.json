{"name": "furniture-store-frontend", "version": "1.0.0", "description": "Frontend for furniture store with AI detection", "private": true, "dependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.2.0", "axios": "^1.5.0", "react-router-dom": "^6.16.0", "tailwindcss": "^3.3.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "framer-motion": "^10.16.0", "react-dropzone": "^14.2.0", "react-hot-toast": "^2.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.0"}}