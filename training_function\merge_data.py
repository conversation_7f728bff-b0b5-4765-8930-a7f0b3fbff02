# create_val_set.py
import os
import random
import shutil
from tqdm import tqdm

SOURCE_IMAGES = "processed_data/images/train"
SOURCE_LABELS = "processed_data/labels/train"

DEST_IMAGES = "processed_data/images/val"
DEST_LABELS = "processed_data/labels/val"

# Tỷ lệ dữ liệu bạn muốn chuyển sang tập validation (vd: 0.15 = 15%)
VAL_SPLIT_RATIO = 0.15

def main():
    # Đ<PERSON><PERSON> bảo thư mục đích đã tồn tại
    os.makedirs(DEST_IMAGES, exist_ok=True)
    os.makedirs(DEST_LABELS, exist_ok=True)

    all_images = [f for f in os.listdir(SOURCE_IMAGES) if f.endswith(('.jpg', '.png'))]
    random.shuffle(all_images)

    num_val_images = int(len(all_images) * VAL_SPLIT_RATIO)
    val_images = all_images[:num_val_images]

    print(f"Tổng số ảnh trong tập train: {len(all_images)}")
    print(f"Sẽ di chuyển {len(val_images)} <PERSON><PERSON> sang tập validation...")

    for img_filename in tqdm(val_images, desc="Di chuyển file sang tập validation"):
        label_filename = os.path.splitext(img_filename)[0] + '.txt'

        # Di chuyển file ảnh
        shutil.move(os.path.join(SOURCE_IMAGES, img_filename), os.path.join(DEST_IMAGES, img_filename))
        
        # Di chuyển file label tương ứng
        source_label_path = os.path.join(SOURCE_LABELS, label_filename)
        if os.path.exists(source_label_path):
            shutil.move(source_label_path, os.path.join(DEST_LABELS, label_filename))
    
    print("\n✅ Hoàn tất việc tạo tập validation!")

if __name__ == '__main__':
    main()