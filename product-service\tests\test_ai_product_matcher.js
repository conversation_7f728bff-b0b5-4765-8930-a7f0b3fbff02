const { expect } = require('chai');
const sinon = require('sinon');
const AIProductMatcher = require('../src/services/ai_product_matcher');

describe('AIProductMatcher', () => {
    let aiMatcher;
    let mockPool;

    beforeEach(() => {
        // Mock database pool
        mockPool = {
            query: sinon.stub()
        };

        // Create AIProductMatcher instance
        aiMatcher = new AIProductMatcher();
        
        // Replace pool with mock
        const originalPool = require('../src/config/database').pool;
        sinon.stub(require('../src/config/database'), 'pool').value(mockPool);
    });

    afterEach(() => {
        sinon.restore();
    });

    describe('Object to Product Mapping', () => {
        it('should have correct mapping for furniture objects', () => {
            const mapping = aiMatcher.objectToProductMapping;

            expect(mapping).to.have.property('chair');
            expect(mapping.chair.categories).to.include('Chairs');
            expect(mapping.chair.keywords).to.include('chair');
            expect(mapping.chair.priority).to.equal(1.0);

            expect(mapping).to.have.property('sofa');
            expect(mapping.sofa.categories).to.include('Sofas');
            
            expect(mapping).to.have.property('table');
            expect(mapping.table.categories).to.include('Tables');
        });

        it('should have lower priority for small objects', () => {
            const mapping = aiMatcher.objectToProductMapping;

            expect(mapping.cup.priority).to.be.lessThan(mapping.chair.priority);
            expect(mapping.plate.priority).to.be.lessThan(mapping.table.priority);
            expect(mapping.pillow.priority).to.be.lessThan(mapping.sofa.priority);
        });
    });

    describe('Confidence Weight Calculation', () => {
        it('should return correct weights for different confidence levels', () => {
            expect(aiMatcher.getConfidenceWeight(0.8)).to.equal(1.0); // High confidence
            expect(aiMatcher.getConfidenceWeight(0.5)).to.equal(0.8); // Medium confidence
            expect(aiMatcher.getConfidenceWeight(0.3)).to.equal(0.5); // Low confidence
            expect(aiMatcher.getConfidenceWeight(0.1)).to.equal(0.5); // Very low confidence
        });
    });

    describe('Search Query Generation', () => {
        it('should generate correct search queries for detected objects', () => {
            const detectedObjects = [
                { class_name: 'chair', confidence: 0.8 },
                { class_name: 'table', confidence: 0.6 },
                { class_name: 'unknown_object', confidence: 0.5 }
            ];

            const searchData = aiMatcher.generateSearchQueries(detectedObjects);

            expect(searchData.queries).to.be.an('array');
            expect(searchData.categories).to.be.an('array');
            expect(searchData.keywords).to.be.an('array');

            // Should include chair and table categories
            expect(searchData.categories).to.include('Chairs');
            expect(searchData.categories).to.include('Tables');

            // Should include keywords
            expect(searchData.keywords).to.include('chair');
            expect(searchData.keywords).to.include('table');

            // Should handle unknown objects
            const unknownQuery = searchData.queries.find(q => q.source === 'unknown_object');
            expect(unknownQuery).to.exist;
            expect(unknownQuery.text).to.equal('unknown_object');
        });

        it('should sort queries by weight', () => {
            const detectedObjects = [
                { class_name: 'cup', confidence: 0.9 },      // Low priority, high confidence
                { class_name: 'chair', confidence: 0.7 }     // High priority, medium confidence
            ];

            const searchData = aiMatcher.generateSearchQueries(detectedObjects);

            // Chair should have higher weight due to higher priority
            const chairQueries = searchData.queries.filter(q => q.source === 'chair');
            const cupQueries = searchData.queries.filter(q => q.source === 'cup');

            expect(chairQueries[0].weight).to.be.greaterThan(cupQueries[0].weight);
        });
    });

    describe('Product Search', () => {
        it('should search products with correct SQL query', async () => {
            const searchData = {
                queries: [
                    { text: 'chair', weight: 1.0, source: 'chair', confidence: 0.8 },
                    { text: 'table', weight: 0.8, source: 'table', confidence: 0.6 }
                ],
                categories: ['Chairs', 'Tables'],
                keywords: ['chair', 'table']
            };

            const mockProducts = [
                {
                    id: 1,
                    name: 'Modern Chair',
                    price: '299.99',
                    sale_price: null,
                    category_name: 'Chairs',
                    primary_image: '/images/chair1.jpg',
                    relevance_score: 0.95
                },
                {
                    id: 2,
                    name: 'Dining Table',
                    price: '599.99',
                    sale_price: '499.99',
                    category_name: 'Tables',
                    primary_image: '/images/table1.jpg',
                    relevance_score: 0.87
                }
            ];

            mockPool.query.resolves({ rows: mockProducts });

            const results = await aiMatcher.searchProducts(searchData, 10, 'relevance');

            expect(mockPool.query.calledOnce).to.be.true;
            expect(results).to.be.an('array');
            expect(results).to.have.lengthOf(2);
            
            // Check price conversion
            expect(results[0].price).to.equal(299.99);
            expect(results[1].sale_price).to.equal(499.99);
        });

        it('should handle empty search data', async () => {
            const searchData = {
                queries: [],
                categories: [],
                keywords: []
            };

            const results = await aiMatcher.searchProducts(searchData, 10, 'relevance');
            expect(results).to.be.an('array');
            expect(results).to.have.lengthOf(0);
        });
    });

    describe('Find Products for Detected Objects', () => {
        it('should find products for valid detected objects', async () => {
            const detectedObjects = [
                { class_name: 'chair', confidence: 0.8 },
                { class_name: 'table', confidence: 0.6 }
            ];

            const mockProducts = [
                { id: 1, name: 'Chair', price: '299.99', category_name: 'Chairs' }
            ];

            const mockCategories = [
                { id: 1, name: 'Chairs', product_count: 15 }
            ];

            // Mock searchProducts and getRelevantCategories
            sinon.stub(aiMatcher, 'searchProducts').resolves(mockProducts);
            sinon.stub(aiMatcher, 'getRelevantCategories').resolves(mockCategories);

            const result = await aiMatcher.findProductsForDetectedObjects(detectedObjects);

            expect(result).to.have.property('products');
            expect(result).to.have.property('categories');
            expect(result).to.have.property('searchQueries');
            expect(result).to.have.property('detectedObjectsCount');
            expect(result).to.have.property('totalConfidence');

            expect(result.products).to.equal(mockProducts);
            expect(result.categories).to.equal(mockCategories);
            expect(result.detectedObjectsCount).to.equal(2);
        });

        it('should filter objects by minimum confidence', async () => {
            const detectedObjects = [
                { class_name: 'chair', confidence: 0.8 },
                { class_name: 'table', confidence: 0.1 }  // Below minimum
            ];

            sinon.stub(aiMatcher, 'searchProducts').resolves([]);
            sinon.stub(aiMatcher, 'getRelevantCategories').resolves([]);

            const result = await aiMatcher.findProductsForDetectedObjects(detectedObjects, {
                minConfidence: 0.2
            });

            expect(result.detectedObjectsCount).to.equal(1); // Only chair should be included
        });

        it('should return empty result for no valid objects', async () => {
            const detectedObjects = [
                { class_name: 'chair', confidence: 0.1 }  // Below minimum
            ];

            const result = await aiMatcher.findProductsForDetectedObjects(detectedObjects, {
                minConfidence: 0.2
            });

            expect(result.products).to.be.an('array');
            expect(result.products).to.have.lengthOf(0);
            expect(result.categories).to.be.an('array');
            expect(result.categories).to.have.lengthOf(0);
        });
    });

    describe('Get Relevant Categories', () => {
        it('should get categories with product counts', async () => {
            const searchData = {
                categories: ['Chairs', 'Tables']
            };

            const mockCategories = [
                { id: 1, name: 'Chairs', description: 'Seating furniture', product_count: 25 },
                { id: 2, name: 'Tables', description: 'Table furniture', product_count: 18 }
            ];

            mockPool.query.resolves({ rows: mockCategories });

            const result = await aiMatcher.getRelevantCategories(searchData);

            expect(mockPool.query.calledOnce).to.be.true;
            expect(result).to.equal(mockCategories);
        });

        it('should return empty array for no categories', async () => {
            const searchData = { categories: [] };

            const result = await aiMatcher.getRelevantCategories(searchData);

            expect(result).to.be.an('array');
            expect(result).to.have.lengthOf(0);
        });
    });

    describe('Calculate Total Confidence', () => {
        it('should calculate average confidence correctly', () => {
            const detectedObjects = [
                { confidence: 0.8 },
                { confidence: 0.6 },
                { confidence: 0.4 }
            ];

            const totalConfidence = aiMatcher.calculateTotalConfidence(detectedObjects);
            expect(totalConfidence).to.equal(0.6); // (0.8 + 0.6 + 0.4) / 3
        });

        it('should return 0 for empty array', () => {
            const totalConfidence = aiMatcher.calculateTotalConfidence([]);
            expect(totalConfidence).to.equal(0);
        });
    });

    describe('Get Recommendations for Object', () => {
        it('should get recommendations for single object', async () => {
            const mockResult = {
                products: [{ id: 1, name: 'Chair' }],
                categories: [],
                searchQueries: [],
                detectedObjectsCount: 1,
                totalConfidence: 0.8
            };

            sinon.stub(aiMatcher, 'findProductsForDetectedObjects').resolves(mockResult);

            const result = await aiMatcher.getRecommendationsForObject('chair', 0.8, 10);

            expect(result).to.equal(mockResult);
            expect(aiMatcher.findProductsForDetectedObjects.calledOnce).to.be.true;
            
            const calledArgs = aiMatcher.findProductsForDetectedObjects.getCall(0).args;
            expect(calledArgs[0]).to.deep.equal([{ class_name: 'chair', confidence: 0.8 }]);
            expect(calledArgs[1]).to.deep.equal({ limit: 10 });
        });
    });

    describe('Error Handling', () => {
        it('should handle database errors gracefully', async () => {
            const detectedObjects = [
                { class_name: 'chair', confidence: 0.8 }
            ];

            mockPool.query.rejects(new Error('Database connection failed'));

            try {
                await aiMatcher.findProductsForDetectedObjects(detectedObjects);
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).to.equal('Database connection failed');
            }
        });

        it('should handle invalid detected objects format', async () => {
            const invalidObjects = [
                { invalid: 'data' },
                { class_name: 'chair' }, // Missing confidence
                { confidence: 0.8 }       // Missing class_name
            ];

            sinon.stub(aiMatcher, 'searchProducts').resolves([]);
            sinon.stub(aiMatcher, 'getRelevantCategories').resolves([]);

            const result = await aiMatcher.findProductsForDetectedObjects(invalidObjects);

            expect(result.detectedObjectsCount).to.equal(0);
            expect(result.products).to.have.lengthOf(0);
        });
    });
});

describe('AIProductMatcher Integration Tests', () => {
    let aiMatcher;

    beforeEach(() => {
        aiMatcher = new AIProductMatcher();
    });

    it('should handle real-world detection scenario', async () => {
        const detectedObjects = [
            { class_name: 'sofa', confidence: 0.89 },
            { class_name: 'table', confidence: 0.76 },
            { class_name: 'lamp', confidence: 0.68 },
            { class_name: 'pillow', confidence: 0.45 },
            { class_name: 'rug', confidence: 0.72 }
        ];

        // Mock database responses
        const mockPool = {
            query: sinon.stub()
        };

        // Mock products response
        mockPool.query.onFirstCall().resolves({
            rows: [
                { id: 1, name: 'Modern Sofa', price: '1299.99', category_name: 'Sofas', relevance_score: 0.95 },
                { id: 2, name: 'Coffee Table', price: '399.99', category_name: 'Tables', relevance_score: 0.87 },
                { id: 3, name: 'Floor Lamp', price: '199.99', category_name: 'Lighting', relevance_score: 0.82 }
            ]
        });

        // Mock categories response
        mockPool.query.onSecondCall().resolves({
            rows: [
                { id: 1, name: 'Sofas', product_count: 45 },
                { id: 2, name: 'Tables', product_count: 32 },
                { id: 3, name: 'Lighting', product_count: 28 }
            ]
        });

        sinon.stub(require('../src/config/database'), 'pool').value(mockPool);

        const result = await aiMatcher.findProductsForDetectedObjects(detectedObjects, {
            limit: 20,
            includeCategories: true,
            minConfidence: 0.2,
            sortBy: 'relevance'
        });

        expect(result.products).to.have.lengthOf(3);
        expect(result.categories).to.have.lengthOf(3);
        expect(result.detectedObjectsCount).to.equal(5);
        expect(result.totalConfidence).to.be.greaterThan(0.6);

        sinon.restore();
    });
});
