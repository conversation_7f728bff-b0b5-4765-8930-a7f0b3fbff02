const express = require('express');
const router = express.Router();
const { pool, redisClient } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const categorySchema = Joi.object({
  name: Joi.string().required().max(100),
  description: Joi.string().allow(''),
  parent_id: Joi.number().integer().positive().allow(null),
  image_url: Joi.string().uri().allow('')
});

const updateCategorySchema = Joi.object({
  name: Joi.string().max(100),
  description: Joi.string().allow(''),
  parent_id: Joi.number().integer().positive().allow(null),
  image_url: Joi.string().uri().allow(''),
  is_active: Joi.boolean()
});

// GET /api/categories - L<PERSON>y danh sách danh mục
router.get('/', async (req, res) => {
  try {
    const { include_products = false } = req.query;

    // Check cache first
    const cacheKey = `categories:${include_products}`;
    const cachedCategories = await redisClient.get(cacheKey);
    
    if (cachedCategories) {
      return res.json(JSON.parse(cachedCategories));
    }

    let query = `
      SELECT c.*, 
             pc.name as parent_name,
             (
               SELECT COUNT(*) 
               FROM products p 
               WHERE p.is_active = true AND p.category_id IN (
                 WITH RECURSIVE subcategories AS (
                   SELECT id FROM categories WHERE id = c.id
                   UNION ALL
                   SELECT cat.id FROM categories cat
                   INNER JOIN subcategories s ON s.id = cat.parent_id
                 )
                 SELECT id FROM subcategories
               )
             ) as product_count
      FROM categories c
      LEFT JOIN categories pc ON c.parent_id = pc.id
      WHERE c.is_active = true
      ORDER BY c.parent_id NULLS FIRST, c.name
    `;

    const result = await pool.query(query);
    let categories = result.rows;

    // If include_products is true, get products for each category
    if (include_products === 'true') {
      for (let category of categories) {
        const productsQuery = `
          SELECT id, name, price, sale_price,
                 (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image
          FROM products p
          WHERE category_id = $1 AND is_active = true
          ORDER BY created_at DESC
          LIMIT 10
        `;
        
        const productsResult = await pool.query(productsQuery, [category.id]);
        category.products = productsResult.rows;
      }
    }

    // Organize into tree structure
    const categoryTree = buildCategoryTree(categories);

    // Cache for 30 minutes
    await redisClient.setEx(cacheKey, 1800, JSON.stringify(categoryTree));

    res.json(categoryTree);

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/categories/:id - Lấy chi tiết danh mục
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check cache first
    const cacheKey = `category:${id}`;
    const cachedCategory = await redisClient.get(cacheKey);
    
    if (cachedCategory) {
      return res.json(JSON.parse(cachedCategory));
    }

    const query = `
      SELECT c.*, 
             pc.name as parent_name,
             (
               SELECT COUNT(*) FROM products p 
               WHERE p.is_active = true AND p.category_id IN (
                 WITH RECURSIVE subcategories AS (
                   SELECT id FROM categories WHERE id = c.id
                   UNION ALL
                   SELECT cat.id FROM categories cat
                   INNER JOIN subcategories s ON s.id = cat.parent_id
                 )
                 SELECT id FROM subcategories
               )
             ) as product_count
      FROM categories c
      LEFT JOIN categories pc ON c.parent_id = pc.id
      WHERE c.id = $1 AND c.is_active = true
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    const category = result.rows[0];

    // Get subcategories
    const subcategoriesQuery = `
      SELECT id, name, description, image_url,
             (
               SELECT COUNT(*) FROM products p 
               WHERE p.is_active = true AND p.category_id IN (
                 WITH RECURSIVE subcategories AS (
                   SELECT id FROM categories WHERE id = c.id
                   UNION ALL
                   SELECT cat.id FROM categories cat
                   INNER JOIN subcategories s ON s.id = cat.parent_id
                 )
                 SELECT id FROM subcategories
               )
             ) as product_count
      FROM categories c
      WHERE parent_id = $1 AND is_active = true
      ORDER BY name
    `;

    const subcategoriesResult = await pool.query(subcategoriesQuery, [id]);
    category.subcategories = subcategoriesResult.rows;

    // Cache for 1 hour
    await redisClient.setEx(cacheKey, 3600, JSON.stringify(category));

    res.json(category);

  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/categories - Tạo danh mục mới
router.post('/', async (req, res) => {
  try {
    const { error, value } = categorySchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { name, description, parent_id, image_url } = value;

    // Check if parent category exists (if provided)
    if (parent_id) {
      const parentCheck = await pool.query(
        'SELECT id FROM categories WHERE id = $1 AND is_active = true',
        [parent_id]
      );
      
      if (parentCheck.rows.length === 0) {
        return res.status(400).json({ error: 'Parent category not found' });
      }
    }

    const query = `
      INSERT INTO categories (name, description, parent_id, image_url)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await pool.query(query, [name, description, parent_id, image_url]);

    // Clear categories cache
    await clearCategoriesCache();

    res.status(201).json(result.rows[0]);

  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/categories/:id - Cập nhật danh mục
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { error, value } = updateCategorySchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if parent_id would create a circular reference
    if (value.parent_id) {
      const circularCheck = await checkCircularReference(id, value.parent_id);
      if (circularCheck) {
        return res.status(400).json({ error: 'Circular reference detected' });
      }
    }

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    Object.keys(value).forEach(key => {
      updateFields.push(`${key} = $${paramIndex}`);
      updateValues.push(value[key]);
      paramIndex++;
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updateValues.push(id);

    const query = `
      UPDATE categories 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND is_active = true
      RETURNING *
    `;

    const result = await pool.query(query, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    // Clear cache
    await clearCategoriesCache();
    await redisClient.del(`category:${id}`);

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/categories/:id - Xóa danh mục (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category has products
    const productCheck = await pool.query(
      'SELECT COUNT(*) as count FROM products WHERE category_id = $1 AND is_active = true',
      [id]
    );

    if (parseInt(productCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete category with active products' 
      });
    }

    // Check if category has subcategories
    const subcategoryCheck = await pool.query(
      'SELECT COUNT(*) as count FROM categories WHERE parent_id = $1 AND is_active = true',
      [id]
    );

    if (parseInt(subcategoryCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete category with subcategories' 
      });
    }

    const query = `
      UPDATE categories 
      SET is_active = false
      WHERE id = $1 AND is_active = true
      RETURNING id
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    // Clear cache
    await clearCategoriesCache();
    await redisClient.del(`category:${id}`);

    res.json({ message: 'Category deleted successfully' });

  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Helper functions
function buildCategoryTree(categories) {
  const categoryMap = {};
  const tree = [];

  // Create a map of categories
  categories.forEach(category => {
    categoryMap[category.id] = { ...category, children: [] };
  });

  // Build the tree
  categories.forEach(category => {
    if (category.parent_id) {
      if (categoryMap[category.parent_id]) {
        categoryMap[category.parent_id].children.push(categoryMap[category.id]);
      }
    } else {
      tree.push(categoryMap[category.id]);
    }
  });

  return tree;
}

async function checkCircularReference(categoryId, parentId) {
  const visited = new Set();
  let currentId = parentId;

  while (currentId && !visited.has(currentId)) {
    if (currentId == categoryId) {
      return true; // Circular reference found
    }

    visited.add(currentId);

    const result = await pool.query(
      'SELECT parent_id FROM categories WHERE id = $1',
      [currentId]
    );

    if (result.rows.length === 0) {
      break;
    }

    currentId = result.rows[0].parent_id;
  }

  return false;
}

async function clearCategoriesCache() {
  const keys = await redisClient.keys('categories:*');
  if (keys.length > 0) {
    await redisClient.del(keys);
  }
}

module.exports = router;
