import os
import shutil
import cv2
import numpy as np
from tqdm import tqdm
ADE20K_DIR = "raw_data/ade20k/ade20k/ADEChallengeData2016" # Thay đổi nếu bạn lưu ở nơi khác

# File chứa thông tin các lớp đối tượng
CLASS_INFO_FILE = os.path.join(ADE20K_DIR, "objectInfo150.txt")

# Th<PERSON> mục đầu ra cho dữ liệu đã xử lý
OUTPUT_DIR = "processed_data"

TARGET_CLASSES = {
    'chair': ['chair', 'armchair', 'swivel chair', 'stool', 'seat'],
    'table': ['table', 'desk', 'coffee table', 'kitchen island', 'pool table'],
    'sofa': ['sofa', 'ottoman'],
    'bed': ['bed', 'cradle'],
    'cabinet': ['cabinet', 'chest of drawers', 'wardrobe', 'buffet'],
    'shelf': ['shelf', 'bookcase'],
    'pillow': ['pillow', 'cushion'],
    'lamp': ['lamp', 'chandelier', 'light', 'sconce', 'streetlight'],
    'monitor': ['monitor', 'screen', 'computer', 'television receiver', 'crt screen'],
    'plant': ['plant', 'flower', 'pot', 'palm', 'tree'],
    'bottle': ['bottle', 'can'],
    'plate': ['plate', 'tray'],
    'painting': ['painting', 'poster', 'wall', 'art'], # 'wall' often contains paintings/decor
    'vase': ['vase'],
    'towel': ['towel'],
    'rug': ['rug', 'carpet', 'mat'],
    'mirror': ['mirror'],
    'sink': ['sink', 'bathtub'],
    'toilet': ['toilet'],
    'stove': ['stove', 'oven', 'microwave'],
    'refrigerator': ['refrigerator'],
    'washer': ['washer', 'dishwasher'],
    'curtain': ['curtain', 'blind'],
    'box': ['box', 'case', 'ashcan'],
    'basket': ['basket'],
    'bag': ['bag'],
}

def create_class_map(info_file_path):
    """Đọc file objectInfo150.txt để tạo bản đồ từ ID lớp sang tên lớp."""
    class_map = {}
    with open(info_file_path, 'r') as f:
        for line in f.readlines()[1:]: # Bỏ qua dòng tiêu đề
            parts = line.strip().split("\t")
            if len(parts) > 4:
                try:
                    idx = int(parts[0])
                    names = parts[4].split(',')
                    # Lấy tên ngắn gọn nhất
                    class_map[idx] = names[0].strip()
                except ValueError:
                    continue
    return class_map

def convert_ade20k():
    print("Bắt đầu xử lý ADE20K...")

    ade20k_class_map = create_class_map(CLASS_INFO_FILE)
    unified_class_names = list(TARGET_CLASSES.keys())

    output_images_dir = os.path.join(OUTPUT_DIR, "images", "train")
    output_labels_dir = os.path.join(OUTPUT_DIR, "labels", "train")
    os.makedirs(output_images_dir, exist_ok=True)
    os.makedirs(output_labels_dir, exist_ok=True)

    source_images_dir = os.path.join(ADE20K_DIR, "images", "training")
    source_masks_dir = os.path.join(ADE20K_DIR, "annotations", "training")

    mask_files = os.listdir(source_masks_dir)

    for mask_filename in tqdm(mask_files, desc="Converting Masks"):
        if not mask_filename.endswith('.png'):
            continue

        mask_path = os.path.join(source_masks_dir, mask_filename)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE) # Đọc mask dưới dạng ảnh xám

        if mask is None:
            continue

        img_height, img_width = mask.shape
        yolo_annotations = []

        # Lấy các giá trị pixel (class index) duy nhất trong mask
        object_indices = np.unique(mask)

        for idx in object_indices:
            if idx == 0: continue # Bỏ qua nền (background)

            class_name = ade20k_class_map.get(idx)
            if not class_name: continue

            unified_name = None
            for key, values in TARGET_CLASSES.items():
                if class_name in values:
                    unified_name = key
                    break

            if not unified_name: continue

            class_id = unified_class_names.index(unified_name)

            # Tạo một mask nhị phân cho đối tượng hiện tại
            object_mask = (mask == idx).astype(np.uint8)

            # Tìm các đường viền của đối tượng
            contours, _ = cv2.findContours(object_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) < 20: continue # Bỏ qua các vật thể quá nhỏ

                x, y, w, h = cv2.boundingRect(contour)

                # Chuyển đổi sang định dạng YOLO
                x_center = (x + w / 2) / img_width
                y_center = (y + h / 2) / img_height
                width_norm = w / img_width
                height_norm = h / img_height

                yolo_line = f"{class_id} {x_center:.6f} {y_center:.6f} {width_norm:.6f} {height_norm:.6f}"
                yolo_annotations.append(yolo_line)

        if yolo_annotations:
            img_filename = mask_filename.replace('.png', '.jpg')
            label_filename = mask_filename.replace('.png', '.txt')

            # Ghi file label
            with open(os.path.join(output_labels_dir, label_filename), 'w') as f:
                f.write("\n".join(yolo_annotations))

            # Copy ảnh gốc
            source_img_path = os.path.join(source_images_dir, img_filename)
            if os.path.exists(source_img_path):
                shutil.copy(source_img_path, output_images_dir)

    print("\nHoàn tất chuyển đổi ADE20K!")

if __name__ == '__main__':
    convert_ade20k()