#!/usr/bin/env python3
"""
Comprehensive test script for AI Detection features
Tests the entire pipeline from model training to product search
"""

import os
import sys
import asyncio
import requests
import json
import time
from pathlib import Path
import subprocess
import tempfile
import cv2
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class AIFeaturesTester:
    """Comprehensive tester for AI features"""
    
    def __init__(self):
        self.ai_service_url = "http://localhost:8001"
        self.product_service_url = "http://localhost:3001"
        self.test_results = {}
        self.test_images = []
        
    def create_test_images(self):
        """Create test images for detection"""
        print("Creating test images...")
        
        # Create temporary directory for test images
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a living room scene
        living_room = np.zeros((480, 640, 3), dtype=np.uint8)
        # Add colored rectangles to simulate furniture
        cv2.rectangle(living_room, (100, 200), (400, 350), (139, 69, 19), -1)  # Brown sofa
        cv2.rectangle(living_room, (450, 300), (600, 400), (160, 82, 45), -1)  # Brown table
        cv2.rectangle(living_room, (50, 100), (120, 250), (255, 255, 0), -1)   # Yellow lamp
        
        living_room_path = os.path.join(self.temp_dir, "living_room.jpg")
        cv2.imwrite(living_room_path, living_room)
        self.test_images.append(("living_room", living_room_path))
        
        # Create a bedroom scene
        bedroom = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(bedroom, (200, 150), (600, 400), (128, 0, 128), -1)      # Purple bed
        cv2.rectangle(bedroom, (50, 300), (180, 450), (165, 42, 42), -1)       # Brown dresser
        cv2.rectangle(bedroom, (100, 100), (150, 200), (192, 192, 192), -1)    # Gray mirror
        
        bedroom_path = os.path.join(self.temp_dir, "bedroom.jpg")
        cv2.imwrite(bedroom_path, bedroom)
        self.test_images.append(("bedroom", bedroom_path))
        
        # Create a kitchen scene with small objects
        kitchen = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(kitchen, (100, 100), (300, 400), (139, 69, 19), -1)      # Brown cabinet
        cv2.rectangle(kitchen, (350, 350), (450, 450), (160, 82, 45), -1)      # Brown chair
        cv2.rectangle(kitchen, (400, 300), (500, 350), (160, 82, 45), -1)      # Brown table
        # Small objects
        cv2.circle(kitchen, (430, 320), 10, (255, 255, 255), -1)               # White plate
        cv2.circle(kitchen, (460, 325), 5, (0, 0, 255), -1)                    # Red cup
        
        kitchen_path = os.path.join(self.temp_dir, "kitchen.jpg")
        cv2.imwrite(kitchen_path, kitchen)
        self.test_images.append(("kitchen", kitchen_path))
        
        print(f"Created {len(self.test_images)} test images in {self.temp_dir}")
    
    def cleanup_test_images(self):
        """Clean up test images"""
        import shutil
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print("Cleaned up test images")
    
    def test_ai_service_health(self):
        """Test AI service health"""
        print("\n=== Testing AI Service Health ===")
        try:
            response = requests.get(f"{self.ai_service_url}/health", timeout=10)
            if response.status_code == 200:
                print("✅ AI Service is healthy")
                self.test_results['ai_service_health'] = True
                return True
            else:
                print(f"❌ AI Service health check failed: {response.status_code}")
                self.test_results['ai_service_health'] = False
                return False
        except Exception as e:
            print(f"❌ AI Service is not accessible: {e}")
            self.test_results['ai_service_health'] = False
            return False
    
    def test_product_service_health(self):
        """Test Product service health"""
        print("\n=== Testing Product Service Health ===")
        try:
            response = requests.get(f"{self.product_service_url}/health", timeout=10)
            if response.status_code == 200:
                print("✅ Product Service is healthy")
                self.test_results['product_service_health'] = True
                return True
            else:
                print(f"❌ Product Service health check failed: {response.status_code}")
                self.test_results['product_service_health'] = False
                return False
        except Exception as e:
            print(f"❌ Product Service is not accessible: {e}")
            self.test_results['product_service_health'] = False
            return False
    
    def test_detection_methods(self):
        """Test different detection methods"""
        print("\n=== Testing Detection Methods ===")
        
        methods = ['standard', 'slice', 'super-slice']
        detection_results = {}
        
        for scene_name, image_path in self.test_images:
            print(f"\nTesting scene: {scene_name}")
            scene_results = {}
            
            for method in methods:
                print(f"  Testing {method} detection...")
                try:
                    endpoint = f"/detect"
                    if method == 'slice':
                        endpoint = f"/detect-slice"
                    elif method == 'super-slice':
                        endpoint = f"/detect-super-slice"
                    
                    with open(image_path, 'rb') as f:
                        files = {'file': f}
                        response = requests.post(
                            f"{self.ai_service_url}{endpoint}",
                            files=files,
                            timeout=30
                        )
                    
                    if response.status_code == 200:
                        result = response.json()
                        objects_count = result.get('total_objects', 0)
                        processing_time = result.get('processing_time', 0)
                        
                        print(f"    ✅ {method}: {objects_count} objects, {processing_time:.2f}s")
                        scene_results[method] = {
                            'success': True,
                            'objects_count': objects_count,
                            'processing_time': processing_time,
                            'detected_objects': result.get('detected_objects', [])
                        }
                    else:
                        print(f"    ❌ {method}: HTTP {response.status_code}")
                        scene_results[method] = {'success': False, 'error': response.status_code}
                        
                except Exception as e:
                    print(f"    ❌ {method}: {str(e)}")
                    scene_results[method] = {'success': False, 'error': str(e)}
            
            detection_results[scene_name] = scene_results
        
        self.test_results['detection_methods'] = detection_results
        return detection_results
    
    def test_ai_product_search(self):
        """Test AI-powered product search"""
        print("\n=== Testing AI Product Search ===")
        
        # Test with mock detected objects
        test_objects = [
            {'class_name': 'sofa', 'confidence': 0.89, 'bounding_box': {'x': 100, 'y': 200, 'width': 300, 'height': 150}},
            {'class_name': 'table', 'confidence': 0.76, 'bounding_box': {'x': 450, 'y': 300, 'width': 150, 'height': 100}},
            {'class_name': 'lamp', 'confidence': 0.68, 'bounding_box': {'x': 50, 'y': 100, 'width': 70, 'height': 150}}
        ]
        
        search_results = {}
        
        # Test AI-detected search
        print("Testing AI-detected product search...")
        try:
            response = requests.post(
                f"{self.product_service_url}/api/search/ai-detected",
                json={
                    'detected_objects': test_objects,
                    'limit': 10,
                    'sort_by': 'relevance',
                    'min_confidence': 0.2
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                products_count = len(result.get('products', []))
                categories_count = len(result.get('categories', []))
                
                print(f"  ✅ Found {products_count} products, {categories_count} categories")
                search_results['ai_detected'] = {
                    'success': True,
                    'products_count': products_count,
                    'categories_count': categories_count
                }
            else:
                print(f"  ❌ AI-detected search failed: HTTP {response.status_code}")
                search_results['ai_detected'] = {'success': False, 'error': response.status_code}
                
        except Exception as e:
            print(f"  ❌ AI-detected search error: {str(e)}")
            search_results['ai_detected'] = {'success': False, 'error': str(e)}
        
        # Test single object search
        print("Testing single object search...")
        try:
            response = requests.get(
                f"{self.product_service_url}/api/search/ai-object/sofa",
                params={'confidence': 0.8, 'limit': 5},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                products_count = len(result.get('products', []))
                
                print(f"  ✅ Found {products_count} sofa products")
                search_results['single_object'] = {
                    'success': True,
                    'products_count': products_count
                }
            else:
                print(f"  ❌ Single object search failed: HTTP {response.status_code}")
                search_results['single_object'] = {'success': False, 'error': response.status_code}
                
        except Exception as e:
            print(f"  ❌ Single object search error: {str(e)}")
            search_results['single_object'] = {'success': False, 'error': str(e)}
        
        # Test AI categories
        print("Testing AI categories...")
        try:
            response = requests.get(
                f"{self.product_service_url}/api/search/ai-categories",
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                mappings_count = len(result.get('mappings', {}))
                
                print(f"  ✅ Found {mappings_count} object-category mappings")
                search_results['ai_categories'] = {
                    'success': True,
                    'mappings_count': mappings_count
                }
            else:
                print(f"  ❌ AI categories failed: HTTP {response.status_code}")
                search_results['ai_categories'] = {'success': False, 'error': response.status_code}
                
        except Exception as e:
            print(f"  ❌ AI categories error: {str(e)}")
            search_results['ai_categories'] = {'success': False, 'error': str(e)}
        
        self.test_results['ai_product_search'] = search_results
        return search_results
    
    def test_model_performance(self):
        """Test model performance and accuracy"""
        print("\n=== Testing Model Performance ===")
        
        performance_results = {}
        
        # Test available models
        try:
            response = requests.get(f"{self.ai_service_url}/models/available", timeout=10)
            if response.status_code == 200:
                models_info = response.json()
                available_models = models_info.get('available_models', {})
                current_model = models_info.get('current_model', 'unknown')
                
                print(f"  ✅ Available models: {list(available_models.keys())}")
                print(f"  ✅ Current model: {current_model}")
                
                performance_results['models'] = {
                    'success': True,
                    'available_models': list(available_models.keys()),
                    'current_model': current_model
                }
            else:
                print(f"  ❌ Models info failed: HTTP {response.status_code}")
                performance_results['models'] = {'success': False, 'error': response.status_code}
                
        except Exception as e:
            print(f"  ❌ Models info error: {str(e)}")
            performance_results['models'] = {'success': False, 'error': str(e)}
        
        # Test detection stats
        try:
            response = requests.get(f"{self.ai_service_url}/detection/stats", timeout=10)
            if response.status_code == 200:
                stats = response.json()
                service_type = stats.get('service_type', 'unknown')
                features = stats.get('features', {})
                
                print(f"  ✅ Service type: {service_type}")
                print(f"  ✅ Features: {list(features.keys())}")
                
                performance_results['stats'] = {
                    'success': True,
                    'service_type': service_type,
                    'features': features
                }
            else:
                print(f"  ❌ Detection stats failed: HTTP {response.status_code}")
                performance_results['stats'] = {'success': False, 'error': response.status_code}
                
        except Exception as e:
            print(f"  ❌ Detection stats error: {str(e)}")
            performance_results['stats'] = {'success': False, 'error': str(e)}
        
        self.test_results['model_performance'] = performance_results
        return performance_results
    
    def run_unit_tests(self):
        """Run unit tests for AI services"""
        print("\n=== Running Unit Tests ===")
        
        unit_test_results = {}
        
        # Run AI service tests
        print("Running AI service unit tests...")
        try:
            result = subprocess.run(
                ['python', '-m', 'pytest', 'ai-service/tests/', '-v'],
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("  ✅ AI service unit tests passed")
                unit_test_results['ai_service'] = {'success': True, 'output': result.stdout}
            else:
                print("  ❌ AI service unit tests failed")
                unit_test_results['ai_service'] = {'success': False, 'output': result.stderr}
                
        except Exception as e:
            print(f"  ❌ AI service unit tests error: {str(e)}")
            unit_test_results['ai_service'] = {'success': False, 'error': str(e)}
        
        # Run Product service tests
        print("Running Product service unit tests...")
        try:
            result = subprocess.run(
                ['npm', 'test'],
                cwd=project_root / 'product-service',
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("  ✅ Product service unit tests passed")
                unit_test_results['product_service'] = {'success': True, 'output': result.stdout}
            else:
                print("  ❌ Product service unit tests failed")
                unit_test_results['product_service'] = {'success': False, 'output': result.stderr}
                
        except Exception as e:
            print(f"  ❌ Product service unit tests error: {str(e)}")
            unit_test_results['product_service'] = {'success': False, 'error': str(e)}
        
        self.test_results['unit_tests'] = unit_test_results
        return unit_test_results
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("AI FEATURES TEST REPORT")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            print(f"\n{category.upper().replace('_', ' ')}:")
            
            if isinstance(results, dict):
                if 'success' in results:
                    total_tests += 1
                    if results['success']:
                        passed_tests += 1
                        print(f"  ✅ PASSED")
                    else:
                        print(f"  ❌ FAILED: {results.get('error', 'Unknown error')}")
                else:
                    for test_name, test_result in results.items():
                        total_tests += 1
                        if isinstance(test_result, dict) and test_result.get('success'):
                            passed_tests += 1
                            print(f"  ✅ {test_name}: PASSED")
                        else:
                            print(f"  ❌ {test_name}: FAILED")
            else:
                total_tests += 1
                if results:
                    passed_tests += 1
                    print(f"  ✅ PASSED")
                else:
                    print(f"  ❌ FAILED")
        
        print(f"\n" + "="*60)
        print(f"SUMMARY: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
        print("="*60)
        
        # Save detailed report
        report_path = project_root / "test_report.json"
        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"Detailed report saved to: {report_path}")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """Run all tests"""
        print("Starting comprehensive AI features testing...")
        
        try:
            # Create test images
            self.create_test_images()
            
            # Run all tests
            self.test_ai_service_health()
            self.test_product_service_health()
            self.test_detection_methods()
            self.test_ai_product_search()
            self.test_model_performance()
            self.run_unit_tests()
            
            # Generate report
            success = self.generate_report()
            
            return success
            
        finally:
            # Cleanup
            self.cleanup_test_images()

def main():
    """Main function"""
    tester = AIFeaturesTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! AI features are working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the report for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
