{"name": "user-service", "version": "1.0.0", "description": "User management service for furniture store", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "pg": "^8.11.3", "redis": "^4.6.10", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["user", "authentication", "microservice"], "author": "Your Name", "license": "MIT"}