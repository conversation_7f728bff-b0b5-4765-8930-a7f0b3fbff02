import pytest
import asyncio
import os
import tempfile
from pathlib import Path
import cv2
import numpy as np
from unittest.mock import Mock, patch, AsyncMock

# Import the services to test
from services.enhanced_detection import EnhancedObjectDetectionService
from models.detection_result import DetectionR<PERSON>ult, BoundingBox, DetectedObject

class TestEnhancedObjectDetectionService:
    """Test suite for Enhanced Object Detection Service"""
    
    @pytest.fixture
    def detection_service(self):
        """Create a detection service instance for testing"""
        # Mock the config to avoid loading actual models
        with patch('services.enhanced_detection.Path.exists', return_value=False):
            service = EnhancedObjectDetectionService()
            # Mock the models
            service.models = {
                'test_model': {
                    'model': Mock(),
                    'config': {
                        'confidence': 0.25,
                        'iou': 0.45,
                        'description': 'Test model'
                    }
                }
            }
            service.current_model_name = 'test_model'
            return service
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample test image"""
        # Create a temporary image file
        temp_dir = tempfile.mkdtemp()
        image_path = os.path.join(temp_dir, 'test_image.jpg')
        
        # Create a simple test image
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        image[100:200, 100:200] = [255, 0, 0]  # Red square
        image[300:400, 300:400] = [0, 255, 0]  # Green square
        
        cv2.imwrite(image_path, image)
        yield image_path
        
        # Cleanup
        os.remove(image_path)
        os.rmdir(temp_dir)
    
    def test_load_config(self, detection_service):
        """Test configuration loading"""
        assert detection_service.config is not None
        assert 'models' in detection_service.config
        assert 'detection_settings' in detection_service.config
        assert 'class_confidence_thresholds' in detection_service.config
    
    def test_load_furniture_classes(self, detection_service):
        """Test furniture class mappings"""
        furniture_classes = detection_service.furniture_classes
        
        assert isinstance(furniture_classes, dict)
        assert 'chair' in furniture_classes
        assert 'table' in furniture_classes
        assert 'sofa' in furniture_classes
        assert furniture_classes['chair'] == 'Chairs and Seating'
    
    def test_set_model(self, detection_service):
        """Test model switching functionality"""
        # Test switching to existing model
        result = detection_service.set_model('test_model')
        assert result is True
        assert detection_service.current_model_name == 'test_model'
        
        # Test switching to non-existing model
        result = detection_service.set_model('non_existing_model')
        assert result is False
        assert detection_service.current_model_name == 'test_model'  # Should remain unchanged
    
    def test_get_current_model(self, detection_service):
        """Test getting current model"""
        model, config = detection_service.get_current_model()
        
        assert model is not None
        assert isinstance(config, dict)
        assert 'confidence' in config
        assert 'iou' in config
    
    def test_confidence_weight_calculation(self, detection_service):
        """Test confidence weight calculation"""
        # High confidence
        weight = detection_service.get_confidence_weight(0.8)
        assert weight == 1.0
        
        # Medium confidence
        weight = detection_service.get_confidence_weight(0.5)
        assert weight == 0.8
        
        # Low confidence
        weight = detection_service.get_confidence_weight(0.3)
        assert weight == 0.5
    
    def test_calculate_iou(self, detection_service):
        """Test IoU calculation"""
        box1 = BoundingBox(x=10, y=10, width=50, height=50)
        box2 = BoundingBox(x=30, y=30, width=50, height=50)
        
        iou = detection_service._calculate_iou(box1, box2)
        
        # Expected IoU for these overlapping boxes
        intersection_area = 30 * 30  # 900
        union_area = 50 * 50 + 50 * 50 - intersection_area  # 4100
        expected_iou = intersection_area / union_area
        
        assert abs(iou - expected_iou) < 0.001
    
    def test_non_max_suppression(self, detection_service):
        """Test Non-Maximum Suppression"""
        # Create overlapping detections
        detections = [
            DetectedObject(
                class_name='chair',
                class_id=0,
                confidence=0.9,
                bounding_box=BoundingBox(x=10, y=10, width=50, height=50)
            ),
            DetectedObject(
                class_name='chair',
                class_id=0,
                confidence=0.8,
                bounding_box=BoundingBox(x=15, y=15, width=50, height=50)  # Overlapping
            ),
            DetectedObject(
                class_name='chair',
                class_id=0,
                confidence=0.7,
                bounding_box=BoundingBox(x=100, y=100, width=50, height=50)  # Non-overlapping
            )
        ]
        
        filtered = detection_service._non_max_suppression(detections, iou_threshold=0.5)
        
        # Should keep the highest confidence overlapping detection and the non-overlapping one
        assert len(filtered) == 2
        assert filtered[0].confidence == 0.9
        assert filtered[1].confidence == 0.7
    
    def test_furniture_filtering(self, detection_service):
        """Test furniture-specific filtering"""
        detections = [
            DetectedObject(class_name='chair', class_id=0, confidence=0.8, bounding_box=BoundingBox(0, 0, 50, 50)),
            DetectedObject(class_name='person', class_id=1, confidence=0.9, bounding_box=BoundingBox(0, 0, 50, 50)),
            DetectedObject(class_name='car', class_id=2, confidence=0.7, bounding_box=BoundingBox(0, 0, 50, 50)),
            DetectedObject(class_name='table', class_id=3, confidence=0.6, bounding_box=BoundingBox(0, 0, 50, 50))
        ]
        
        filtered = detection_service._apply_furniture_filtering(detections)
        
        # Should keep furniture objects and some general objects
        assert len(filtered) == 3  # chair, person, table (car should be filtered out)
        class_names = [obj.class_name for obj in filtered]
        assert 'chair' in class_names
        assert 'table' in class_names
        assert 'person' in class_names
        assert 'car' not in class_names
    
    def test_generate_slice_coordinates(self, detection_service):
        """Test slice coordinate generation"""
        width, height = 640, 480
        grid_size = (2, 2)
        overlap = 0.2
        
        slices = detection_service._generate_slice_coordinates(width, height, grid_size, overlap)
        
        assert len(slices) == 4  # 2x2 grid
        
        # Check that all slices have valid coordinates
        for x, y, w, h in slices:
            assert x >= 0
            assert y >= 0
            assert w > 0
            assert h > 0
    
    @pytest.mark.asyncio
    async def test_detect_objects_mock(self, detection_service, sample_image):
        """Test object detection with mocked YOLO results"""
        # Mock YOLO results
        mock_result = Mock()
        mock_result.boxes = Mock()
        
        # Create mock boxes
        mock_box = Mock()
        mock_box.xyxy = [Mock()]
        mock_box.xyxy[0].cpu.return_value.numpy.return_value = [10, 10, 60, 60]
        mock_box.conf = [Mock()]
        mock_box.conf[0].cpu.return_value.numpy.return_value = 0.8
        mock_box.cls = [Mock()]
        mock_box.cls[0].cpu.return_value.numpy.return_value = 0
        
        mock_result.boxes = [mock_box]
        mock_result.names = {0: 'chair'}
        
        # Mock the model call
        detection_service.models['test_model']['model'].return_value = [mock_result]
        
        # Test detection
        result = await detection_service.detect_objects(sample_image)
        
        assert isinstance(result, DetectionResult)
        assert result.total_objects >= 0
        assert result.method.startswith('enhanced_')
    
    def test_scale_image(self, detection_service):
        """Test image scaling functionality"""
        # Create a test image
        image = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # Test scaling up
        scaled_up = detection_service._scale_image(image, 1.5)
        assert scaled_up.shape == (150, 150, 3)
        
        # Test scaling down
        scaled_down = detection_service._scale_image(image, 0.5)
        assert scaled_down.shape == (50, 50, 3)
        
        # Test no scaling
        no_scale = detection_service._scale_image(image, 1.0)
        assert np.array_equal(no_scale, image)

class TestDetectionIntegration:
    """Integration tests for the detection system"""
    
    @pytest.mark.asyncio
    async def test_full_detection_pipeline(self):
        """Test the complete detection pipeline"""
        # This would require actual model files, so we'll mock it
        with patch('services.enhanced_detection.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            # Create service
            service = EnhancedObjectDetectionService()
            
            # Mock detection results
            mock_result = Mock()
            mock_result.boxes = []
            mock_model.return_value = [mock_result]
            
            # Create temporary image
            temp_dir = tempfile.mkdtemp()
            image_path = os.path.join(temp_dir, 'test.jpg')
            image = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.imwrite(image_path, image)
            
            try:
                # Test detection
                result = await service.detect_objects(image_path)
                assert isinstance(result, DetectionResult)
                
            finally:
                # Cleanup
                os.remove(image_path)
                os.rmdir(temp_dir)

if __name__ == '__main__':
    pytest.main([__file__])
