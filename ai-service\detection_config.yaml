# Enhanced Object Detection Configuration
# Optimized for furniture detection with multiple models and fine-tuning

# Model configurations
models:
  default:
    path: 'models/yolo11s-seg.pt'
    type: 'segmentation'
    confidence: 0.25
    iou: 0.45
    description: 'Default YOLOv11 segmentation model'
    
  furniture_finetuned:
    path: 'models/furniture_yolo11s_best.pt'
    type: 'detection'
    confidence: 0.3
    iou: 0.5
    description: 'Fine-tuned model on HomeObjects-3K furniture dataset'
    
  small_objects:
    path: 'models/small_objects_yolo11s.pt'
    type: 'detection'
    confidence: 0.2
    iou: 0.4
    description: 'Specialized model for small furniture objects'
    
  general_furniture:
    path: 'models/yolo11s.pt'
    type: 'detection'
    confidence: 0.25
    iou: 0.45
    description: 'General purpose YOLO model'

# Detection settings
detection_settings:
  max_det: 300              # Maximum detections per image
  agnostic_nms: false       # Class-agnostic NMS
  retina_masks: true        # High-quality masks for segmentation
  half: false               # Half precision inference
  device: 'auto'            # Auto-detect GPU/CPU
  
# Class-specific confidence thresholds
# Lower thresholds for small/hard-to-detect objects
class_confidence_thresholds:
  # Small objects - lower thresholds
  cup: 0.15
  plate: 0.15
  bowl: 0.15
  book: 0.18
  clock: 0.20
  vase: 0.20
  pillow: 0.18
  
  # Medium objects
  lamp: 0.25
  mirror: 0.25
  picture: 0.25
  plant: 0.22
  
  # Large furniture - higher thresholds for precision
  chair: 0.30
  table: 0.30
  sofa: 0.35
  bed: 0.35
  desk: 0.30
  cabinet: 0.32
  shelf: 0.28
  
  # Textiles
  curtain: 0.25
  rug: 0.28
  carpet: 0.30
  
  # Storage
  drawer: 0.25
  dresser: 0.30
  
  # Other furniture
  bench: 0.28
  stool: 0.25

# Small object enhancement settings
small_object_enhancement:
  enabled: true
  min_size: 32              # Minimum object size in pixels
  scale_factors: [0.8, 1.0, 1.2, 1.4]  # Multi-scale detection
  tta: true                 # Test Time Augmentation
  enhance_classes:          # Classes that benefit from enhancement
    - cup
    - plate
    - bowl
    - book
    - clock
    - vase
    - pillow

# Slicing configurations
slicing:
  standard:
    grid_size: [2, 2]       # 2x2 grid
    overlap: 0.2            # 20% overlap
    confidence_reduction: 0.05  # Reduce confidence threshold by this amount
    
  enhanced:
    grid_size: [3, 3]       # 3x3 grid for large images
    overlap: 0.3            # 30% overlap
    confidence_reduction: 0.1
    
  super:
    grid_size: [4, 3]       # 4x3 grid for maximum detection
    overlap: 0.4            # 40% overlap
    confidence_reduction: 0.15
    multi_scale: true       # Enable multi-scale processing

# NMS (Non-Maximum Suppression) settings
nms:
  default_iou: 0.5          # Default IoU threshold
  class_specific_iou:       # Class-specific IoU thresholds
    chair: 0.4              # Lower IoU for chairs (can be close together)
    table: 0.5
    sofa: 0.6               # Higher IoU for large objects
    bed: 0.6
    cup: 0.3                # Lower IoU for small objects
    plate: 0.3
    bowl: 0.3
    book: 0.3
    
# Post-processing filters
post_processing:
  furniture_only: true      # Filter to keep only furniture-related objects
  min_area: 1024           # Minimum object area (32x32 pixels)
  max_area: 409600         # Maximum object area (640x640 pixels)
  aspect_ratio_filter:
    enabled: true
    min_ratio: 0.1          # Minimum width/height ratio
    max_ratio: 10.0         # Maximum width/height ratio
    
# Furniture category mappings
furniture_categories:
  seating:
    - chair
    - sofa
    - bench
    - stool
    
  tables:
    - table
    - desk
    
  storage:
    - cabinet
    - shelf
    - drawer
    - dresser
    
  bedroom:
    - bed
    - pillow
    
  lighting:
    - lamp
    
  decor:
    - mirror
    - picture
    - vase
    - plant
    - clock
    
  textiles:
    - curtain
    - rug
    - carpet
    
  tableware:
    - plate
    - cup
    - bowl
    
  media:
    - book

# Performance optimization
performance:
  batch_processing: false   # Process images in batches
  cache_models: true        # Cache loaded models in memory
  parallel_slicing: false   # Process slices in parallel (experimental)
  memory_optimization: true # Optimize memory usage
  
# Logging and debugging
logging:
  level: 'INFO'
  log_detections: true      # Log detection results
  save_debug_images: false  # Save images with bounding boxes for debugging
  debug_output_dir: 'debug_output'

# Quality assurance
quality_assurance:
  min_confidence_global: 0.1    # Global minimum confidence
  max_detections_per_class: 50  # Maximum detections per class
  duplicate_removal: true       # Remove duplicate detections
  edge_detection_filter: true   # Filter detections too close to image edges
  edge_threshold: 10            # Pixels from edge to filter

# Model switching rules
model_switching:
  auto_switch: true         # Automatically switch models based on image content
  rules:
    small_objects_detected: 'small_objects'  # Switch to small objects model if many small objects
    large_image: 'furniture_finetuned'       # Use fine-tuned model for large images
    default: 'default'                       # Default model choice
