from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from pathlib import Path
import aiofiles
import uuid
from datetime import datetime

from services.object_detection import ObjectDetectionService
from services.enhanced_detection import EnhancedObjectDetectionService
from services.image_processor import ImageProcessor
from models.detection_result import DetectionResult
from database.connection import get_database_connection
import time

app = FastAPI(
    title="AI Service - Enhanced Furniture Detection",
    description="Advanced furniture detection service with fine-tuned models and optimized algorithms",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
try:
    # Try to use enhanced detection service first
    enhanced_detection_service = EnhancedObjectDetectionService()
    object_detection_service = enhanced_detection_service
    logger.info("Enhanced detection service initialized successfully")
except Exception as e:
    # Fallback to original service
    logger.warning(f"Enhanced detection service failed to initialize: {e}")
    logger.info("Falling back to original detection service")
    object_detection_service = ObjectDetectionService()

image_processor = ImageProcessor()

@app.get("/")
async def root():
    return {"message": "AI Service - Furniture Detection API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/detect", response_model=DetectionResult)
async def detect_furniture(file: UploadFile = File(...), model: str = "default"):
    """
    Enhanced furniture detection from uploaded image
    Supports multiple models and optimized detection algorithms
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Only image files are accepted")

        # Generate unique filename
        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = f"uploads/{unique_filename}"

        # Create uploads directory if not exists
        os.makedirs("uploads", exist_ok=True)

        # Save uploaded file
        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)

        # Measure processing time
        start_time = time.time()

        # Enhanced detection with model selection
        if hasattr(object_detection_service, 'detect_objects'):
            if hasattr(object_detection_service, 'set_model'):
                # Enhanced service with model switching
                detection_result = await object_detection_service.detect_objects(file_path, model_name=model)
            else:
                # Original service
                detection_result = await object_detection_service.detect_objects(file_path)
        else:
            raise HTTPException(status_code=500, detail="Detection service not properly initialized")

        processing_time = time.time() - start_time
        detection_result.processing_time = processing_time
        detection_result.timestamp = datetime.now()

        # Clean up temporary file
        if os.path.exists(file_path):
            os.remove(file_path)

        return detection_result

    except Exception as e:
        # Clean up on error
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Detection error: {str(e)}")

@app.post("/detect-slice")
async def detect_furniture_slice(file: UploadFile = File(...), model: str = "default"):
    """
    Enhanced slicing detection for better small object detection
    Uses adaptive grid slicing with optimized overlap
    """
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Only image files are accepted")

        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = f"uploads/{unique_filename}"

        # Create uploads directory if not exists
        os.makedirs("uploads", exist_ok=True)

        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)

        # Measure processing time
        start_time = time.time()

        # Enhanced slicing detection
        if hasattr(object_detection_service, 'detect_objects_slice'):
            if hasattr(object_detection_service, 'set_model'):
                # Enhanced service with model switching
                detection_result = await object_detection_service.detect_objects_slice(file_path, model_name=model)
            else:
                # Original service
                detection_result = await object_detection_service.detect_objects_slice(file_path)
        else:
            raise HTTPException(status_code=500, detail="Slicing detection not supported")

        processing_time = time.time() - start_time
        detection_result.processing_time = processing_time
        detection_result.timestamp = datetime.now()

        # Clean up temporary file
        if os.path.exists(file_path):
            os.remove(file_path)

        return detection_result

    except Exception as e:
        # Clean up on error
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Slicing detection error: {str(e)}")

@app.post("/detect-super-slice")
async def detect_furniture_super_slice(file: UploadFile = File(...), model: str = "small_objects"):
    """
    Enhanced super slicing detection for maximum small object detection
    Uses 4x3 grid with multi-scale processing and aggressive filtering
    """
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Only image files are accepted")

        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = f"uploads/{unique_filename}"

        # Create uploads directory if not exists
        os.makedirs("uploads", exist_ok=True)

        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)

        # Measure processing time
        start_time = time.time()

        # Enhanced super slicing detection
        if hasattr(object_detection_service, 'detect_objects_super_slice'):
            if hasattr(object_detection_service, 'set_model'):
                # Enhanced service with model switching
                detection_result = await object_detection_service.detect_objects_super_slice(file_path, model_name=model)
            else:
                # Original service
                detection_result = await object_detection_service.detect_objects_super_slice(file_path)
        else:
            raise HTTPException(status_code=500, detail="Super slicing detection not supported")

        processing_time = time.time() - start_time
        detection_result.processing_time = processing_time
        detection_result.timestamp = datetime.now()

        # Clean up temporary file
        if os.path.exists(file_path):
            os.remove(file_path)

        return detection_result

    except Exception as e:
        # Clean up on error
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Super slicing detection error: {str(e)}")

# New enhanced endpoints
@app.get("/models/available")
async def get_available_models():
    """
    Get list of available detection models
    """
    try:
        if hasattr(object_detection_service, 'models'):
            models = {}
            for model_name, model_info in object_detection_service.models.items():
                models[model_name] = {
                    'name': model_name,
                    'config': model_info.get('config', {}),
                    'description': model_info.get('config', {}).get('description', 'No description available')
                }
            return {"available_models": models, "current_model": object_detection_service.current_model_name}
        else:
            return {"available_models": {"default": {"name": "default", "description": "Standard YOLO model"}}, "current_model": "default"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting models: {str(e)}")

@app.post("/models/switch/{model_name}")
async def switch_model(model_name: str):
    """
    Switch to a specific detection model
    """
    try:
        if hasattr(object_detection_service, 'set_model'):
            success = object_detection_service.set_model(model_name)
            if success:
                return {"message": f"Successfully switched to model: {model_name}", "current_model": model_name}
            else:
                raise HTTPException(status_code=404, detail=f"Model '{model_name}' not found")
        else:
            raise HTTPException(status_code=501, detail="Model switching not supported")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error switching model: {str(e)}")

@app.get("/detection/stats")
async def get_detection_stats():
    """
    Get detection service statistics and configuration
    """
    try:
        stats = {
            "service_type": "Enhanced" if hasattr(object_detection_service, 'config') else "Standard",
            "timestamp": datetime.now().isoformat(),
            "models_loaded": len(getattr(object_detection_service, 'models', {})),
            "current_model": getattr(object_detection_service, 'current_model_name', 'unknown'),
            "furniture_classes": len(getattr(object_detection_service, 'furniture_classes', {})),
            "features": {
                "multi_model": hasattr(object_detection_service, 'set_model'),
                "enhanced_slicing": hasattr(object_detection_service, 'detect_objects_slice'),
                "super_slicing": hasattr(object_detection_service, 'detect_objects_super_slice'),
                "confidence_tuning": hasattr(object_detection_service, 'class_confidence_thresholds'),
                "furniture_filtering": hasattr(object_detection_service, '_apply_furniture_filtering')
            }
        }

        if hasattr(object_detection_service, 'config'):
            stats["configuration"] = {
                "detection_settings": object_detection_service.config.get('detection_settings', {}),
                "small_object_enhancement": object_detection_service.config.get('small_object_enhancement', {})
            }

        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

@app.get("/models")
async def list_models():
    """
    Liệt kê các model AI có sẵn
    """
    models_dir = Path("models")
    if not models_dir.exists():
        return {"models": []}
    
    models = [f.name for f in models_dir.iterdir() if f.is_file() and f.suffix in ['.pt', '.pth']]
    return {"models": models}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
