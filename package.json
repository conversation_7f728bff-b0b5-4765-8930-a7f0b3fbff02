{"name": "furniture-store", "version": "1.0.0", "description": "Furniture Store with AI Object Detection - Microservices Architecture", "private": true, "scripts": {"install:all": "npm run install:frontend && npm run install:services && npm run install:ai", "install:frontend": "cd web-frontend && npm install", "install:services": "npm run install:api-gateway && npm run install:user-service && npm run install:product-service && npm run install:order-service", "install:api-gateway": "cd api-gateway && npm install", "install:user-service": "cd user-service && npm install", "install:product-service": "cd product-service && npm install", "install:order-service": "cd order-service && npm install", "install:ai": "cd ai-service && pip install -r requirements.txt", "start": "npm run docker:up", "stop": "docker-compose down", "start:dev": "npm run install:all && npm run docker:up", "docker:up": "docker-compose up --build -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "npm run docker:down && npm run docker:up", "dev:frontend": "cd web-frontend && npm start", "dev:api-gateway": "cd api-gateway && npm run dev", "dev:user-service": "cd user-service && npm run dev", "dev:product-service": "cd product-service && npm run dev", "dev:order-service": "cd order-service && npm run dev", "dev:ai": "cd ai-service && uvicorn main:app --reload --host 0.0.0.0 --port 8001", "setup:models": "chmod +x scripts/setup-models.sh && ./scripts/setup-models.sh", "demo": "echo '🎉 Furniture Store Demo Ready!' && echo '📱 Frontend: http://localhost:3000' && echo '🤖 AI Detection: http://localhost:3000/ai-detection' && echo '📊 Monitoring: http://localhost:3001' && echo '🔧 API Docs: http://localhost:8000/docs'", "build": "npm run build:frontend", "build:frontend": "cd web-frontend && npm run build", "test": "npm run test:frontend && npm run test:services", "test:frontend": "cd web-frontend && npm test", "test:services": "npm run test:api-gateway && npm run test:user-service && npm run test:product-service && npm run test:order-service", "test:api-gateway": "cd api-gateway && npm test", "test:user-service": "cd user-service && npm test", "test:product-service": "cd product-service && npm test", "test:order-service": "cd order-service && npm test", "clean": "npm run docker:down && docker system prune -f", "setup": "npm run install:all && npm run create-env && npm run download-model", "setup:windows": "setup.bat", "setup:complete": "install-all.bat", "start:windows": "start.bat", "start:dev:windows": "start-dev.bat", "stop:windows": "stop.bat", "create-env": "if not exist .env copy .env.example .env", "download-model": "if not exist ai-service\\models\\yolov8n.pt (mkdir ai-service\\models 2>nul & curl -L \"https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt\" -o ai-service\\models\\yolov8n.pt)", "logs:frontend": "docker-compose logs -f web-frontend", "logs:api-gateway": "docker-compose logs -f api-gateway", "logs:ai": "docker-compose logs -f ai-service", "logs:user": "docker-compose logs -f user-service", "logs:product": "docker-compose logs -f product-service", "logs:order": "docker-compose logs -f order-service", "status": "docker-compose ps", "health": "npm run status && echo 'Services status checked'", "db:seed": "node scripts/seed.js"}, "keywords": ["furniture", "ecommerce", "ai", "object-detection", "microservices", "docker", "react", "nodejs", "python", "yolo"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/VinkRasengan/Store_for_furniture.git"}, "workspaces": ["web-frontend", "api-gateway", "user-service", "product-service", "order-service"], "devDependencies": {"concurrently": "^8.2.2", "dotenv": "^16.4.5", "pg": "^8.11.3", "wait-on": "^7.0.1"}}