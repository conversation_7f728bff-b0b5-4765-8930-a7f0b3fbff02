# Shared Libraries

Thư viện dùng chung cho các microservices trong hệ thống bán nội thất.

## C<PERSON>u <PERSON>r<PERSON>c

```
shared-libs/
├── database/           # Database utilities
├── auth/              # Authentication helpers
├── validation/        # Validation schemas
├── logging/           # Logging configuration
├── errors/            # Custom error classes
└── utils/             # Utility functions
```

## Sử Dụng

### Database Utilities
```javascript
const { createConnection, executeQuery } = require('@shared-libs/database');

const connection = await createConnection();
const result = await executeQuery(connection, 'SELECT * FROM users');
```

### Authentication
```javascript
const { generateToken, verifyToken } = require('@shared-libs/auth');

const token = generateToken(userId);
const decoded = verifyToken(token);
```

### Validation
```javascript
const { validateProduct, validateUser } = require('@shared-libs/validation');

const isValid = validateProduct(productData);
```

### Logging
```javascript
const { logger } = require('@shared-libs/logging');

logger.info('User logged in', { userId });
logger.error('Database connection failed', error);
```

## Cài Đặt

```bash
npm install @shared-libs/database @shared-libs/auth @shared-libs/validation
```

## Development

```bash
npm run build
npm run test
npm run lint
```
