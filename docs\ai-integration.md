# Tích Hợp AI - Nhận Diện Đồ Nội Thất

## Tổng Quan
Hệ thống sử dụng YOLO (You Only Look Once) và kỹ thuật slicing để nhận diện đồ nội thất từ ảnh với độ chính xác cao, đặc biệt cho các vật thể nhỏ.

## Kiến Trúc AI Service

### 1. Model YOLO
- **Base Model:** YOLOv11s-seg
- **Chức năng:** Object detection + segmentation
- **Input:** RGB images
- **Output:** Bounding boxes + class labels + confidence scores

### 2. <PERSON><PERSON> Thuật Slicing
Dựa trên nghiên cứu từ [YoloSlicing](https://github.com/jsammarco/YoloSlicing):

#### Standard Detection
- Xử lý toàn bộ ảnh một lần
- Phù hợp với ảnh có độ phân giải thấp
- <PERSON><PERSON><PERSON> độ <PERSON>, độ chính xác trung bình

#### 2x2 Slicing
- <PERSON><PERSON> ảnh thành 4 lát (2x2) với overlap 20%
- Tăng độ chính xác cho vật thể nhỏ
- X<PERSON> lý 4 lần inference, kết hợp kết quả

#### 4x3 Super Slicing
- Chia ảnh thành 12 lát (4x3) với overlap 30%
- Độ chính xác cao nhất cho vật thể rất nhỏ
- Xử lý 12 lần inference, tốc độ chậm hơn

## Luồng Xử Lý

```
Input Image
     │
     ▼
┌─────────────┐
│ Preprocess  │ ← Resize, normalize
└─────────────┘
     │
     ▼
┌─────────────┐
│   Slicing   │ ← Chia ảnh thành lát
└─────────────┘
     │
     ▼
┌─────────────┐
│ YOLO Model  │ ← Inference trên từng lát
└─────────────┘
     │
     ▼
┌─────────────┐
│Coordinate   │ ← Điều chỉnh tọa độ về ảnh gốc
│Adjustment   │
└─────────────┘
     │
     ▼
┌─────────────┐
│    NMS      │ ← Loại bỏ duplicate detections
└─────────────┘
     │
     ▼
┌─────────────┐
│   Output    │ ← Bounding boxes + labels
└─────────────┘
```

## Cài Đặt và Cấu Hình

### 1. Dependencies
```bash
pip install ultralytics opencv-python numpy pillow torch torchvision
```

### 2. Model Files
```
ai-service/
├── models/
│   ├── yolo11s-seg.pt          # Base model
│   ├── furniture-detector.pt    # Fine-tuned model
│   └── config.yaml             # Model configuration
├── uploads/                     # Temporary image storage
└── results/                     # Detection results
```

### 3. Environment Variables
```bash
# AI Service Configuration
MODEL_PATH=models/yolo11s-seg.pt
CONFIDENCE_THRESHOLD=0.5
IOU_THRESHOLD=0.5
SLICE_OVERLAP_2X2=0.2
SLICE_OVERLAP_4X3=0.3
```

## API Endpoints

### 1. Standard Detection
```http
POST /api/ai/detect
Content-Type: multipart/form-data

file: [image file]
```

**Response:**
```json
{
  "image_path": "uploads/image.jpg",
  "detected_objects": [
    {
      "class_name": "chair",
      "class_id": 0,
      "confidence": 0.95,
      "bounding_box": {
        "x": 100,
        "y": 150,
        "width": 200,
        "height": 300
      }
    }
  ],
  "total_objects": 1,
  "method": "standard"
}
```

### 2. Slicing Detection
```http
POST /api/ai/detect-slice
Content-Type: multipart/form-data

file: [image file]
```

**Response:**
```json
{
  "image_path": "uploads/image.jpg",
  "detected_objects": [...],
  "total_objects": 3,
  "method": "slicing_2x2"
}
```

### 3. Super Slicing Detection
```http
POST /api/ai/detect-super-slice
Content-Type: multipart/form-data

file: [image file]
```

## Xử Lý Ảnh

### 1. Preprocessing
```python
def preprocess_image(image_path):
    # Load ảnh
    image = cv2.imread(image_path)
    
    # Resize nếu cần
    if image.shape[0] > 1920 or image.shape[1] > 1920:
        scale = min(1920 / image.shape[0], 1920 / image.shape[1])
        new_width = int(image.shape[1] * scale)
        new_height = int(image.shape[0] * scale)
        image = cv2.resize(image, (new_width, new_height))
    
    return image
```

### 2. Slicing Algorithm
```python
def create_slices(image, rows, cols, overlap):
    height, width = image.shape[:2]
    
    # Tính kích thước lát với overlap
    slice_width = int(width * (1 + overlap) / cols)
    slice_height = int(height * (1 + overlap) / rows)
    
    slices = []
    for row in range(rows):
        for col in range(cols):
            x = int(col * width / cols - slice_width * overlap / 2)
            y = int(row * height / rows - slice_height * overlap / 2)
            
            # Đảm bảo tọa độ không âm
            x = max(0, x)
            y = max(0, y)
            
            slice_img = image[y:y+slice_height, x:x+slice_width]
            slices.append((slice_img, x, y))
    
    return slices
```

### 3. Non-Maximum Suppression
```python
def non_max_suppression(detections, iou_threshold=0.5):
    if not detections:
        return []
    
    # Sắp xếp theo confidence
    detections.sort(key=lambda x: x.confidence, reverse=True)
    
    filtered = []
    for detection in detections:
        is_duplicate = False
        
        for filtered_detection in filtered:
            if calculate_iou(detection.bounding_box, 
                           filtered_detection.bounding_box) > iou_threshold:
                is_duplicate = True
                break
        
        if not is_duplicate:
            filtered.append(detection)
    
    return filtered
```

## Fine-tuning Model

### 1. Dataset Preparation
- **HomeObjects-3K:** 3,000 ảnh nội thất (YOLO format)
- **ADE20K:** 20,000 ảnh (convert từ COCO)
- **SUNRGBD:** 10,000 ảnh indoor (convert format)

### 2. Training Configuration
```yaml
# config.yaml
model: yolo11s-seg.pt
epochs: 100
batch_size: 16
imgsz: 640
data: furniture_dataset.yaml
patience: 20
save: True
```

### 3. Training Command
```bash
# Fine-tune trên HomeObjects-3K
yolo train model=yolo11s-seg.pt data=homeobjects.yaml epochs=100

# Transfer learning với dataset bổ sung
yolo train model=runs/train/exp/weights/best.pt data=combined_dataset.yaml epochs=50
```

## Đánh Giá Model

### 1. Metrics
- **mAP@0.5:** Mean Average Precision at IoU=0.5
- **mAP@0.5:0.95:** Mean Average Precision across IoU thresholds
- **Precision:** Độ chính xác
- **Recall:** Độ bao phủ
- **F1-Score:** Harmonic mean của Precision và Recall

### 2. Validation
```bash
# Validate model
yolo val model=runs/train/exp/weights/best.pt data=validation.yaml

# Test trên ảnh mới
yolo predict model=runs/train/exp/weights/best.pt source=test_images/
```

## Tối Ưu Hóa Performance

### 1. Model Optimization
- **Quantization:** Giảm precision từ FP32 xuống INT8
- **Pruning:** Loại bỏ weights không cần thiết
- **TensorRT:** GPU acceleration

### 2. Inference Optimization
- **Batch Processing:** Xử lý nhiều ảnh cùng lúc
- **Async Processing:** Non-blocking inference
- **Caching:** Cache model và kết quả

### 3. Memory Management
- **GPU Memory:** Efficient memory allocation
- **Image Resizing:** Tối ưu kích thước ảnh
- **Cleanup:** Giải phóng memory sau mỗi inference

## Monitoring và Logging

### 1. Metrics Collection
```python
import time
import logging

class PerformanceMonitor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def measure_inference_time(self, func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            processing_time = end_time - start_time
            self.logger.info(f"Inference time: {processing_time:.3f}s")
            
            return result
        return wrapper
```

### 2. Error Handling
```python
try:
    result = await object_detection_service.detect_objects(image_path)
except ModelLoadError as e:
    logger.error(f"Model loading failed: {e}")
    return {"error": "Model unavailable"}
except InferenceError as e:
    logger.error(f"Inference failed: {e}")
    return {"error": "Processing failed"}
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return {"error": "Internal server error"}
```

## Tích Hợp Frontend

### 1. Image Upload
```typescript
const uploadImage = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await axios.post('/api/ai/detect-slice', formData);
    setDetectionResult(response.data);
  } catch (error) {
    console.error('Detection failed:', error);
  }
};
```

### 2. Display Results
```typescript
const renderBoundingBoxes = (detectedObjects: DetectedObject[]) => {
  return detectedObjects.map((obj, index) => (
    <div
      key={index}
      className="absolute border-2 border-red-500 cursor-pointer"
      style={{
        left: obj.bounding_box.x,
        top: obj.bounding_box.y,
        width: obj.bounding_box.width,
        height: obj.bounding_box.height
      }}
      onClick={() => handleObjectClick(obj)}
    >
      <span className="bg-red-500 text-white px-2 py-1 text-sm">
        {obj.class_name} ({Math.round(obj.confidence * 100)}%)
      </span>
    </div>
  ));
};
```

## Troubleshooting

### 1. Common Issues
- **Model not loading:** Kiểm tra file path và permissions
- **Memory errors:** Giảm batch size hoặc image size
- **Slow inference:** Sử dụng GPU hoặc optimize model
- **Low accuracy:** Fine-tune trên dataset cụ thể

### 2. Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable debug logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
```

## Tài Liệu Tham Khảo

1. [YoloSlicing GitHub](https://github.com/jsammarco/YoloSlicing)
2. [Ultralytics Documentation](https://docs.ultralytics.com/)
3. [YOLO Paper](https://arxiv.org/abs/1506.02640)
4. [HomeObjects-3K Dataset](https://github.com/hamzagorgulu/Small-Object-Detection-with-YOLO)
