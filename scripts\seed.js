require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const { categories, products } = require('./seed-data.js');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});


function getRandomDate() {
  const end = new Date();
  const start = new Date();
  start.setFullYear(start.getFullYear() - 1);
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

async function seed() {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    console.log('Dropping and recreating public schema...');
    await client.query('DROP SCHEMA IF EXISTS public CASCADE; CREATE SCHEMA public;');
    await client.query('SET search_path TO public;');

    console.log('Applying database schema from database/schema.sql...');
    const schemaPath = path.join(__dirname, '..', 'database', 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    await client.query(schemaSql);
    console.log('Schema applied successfully.');

    console.log('Seeding categories...');
    const categoryMap = new Map();
    for (const parent of categories) {
      const parentRes = await client.query('INSERT INTO categories(name, description) VALUES($1, $2) RETURNING id', [parent.name, `${parent.name} category`]);
      const parentId = parentRes.rows[0].id;
      categoryMap.set(parent.name, parentId);

      if (parent.children) {
        for (const childName of parent.children) {
          const childRes = await client.query('INSERT INTO categories(name, parent_id, description) VALUES($1, $2, $3) RETURNING id', [childName, parentId, `${childName} sub-category`]);
          categoryMap.set(childName, childRes.rows[0].id);
        }
      }
    }
    console.log(`${categoryMap.size} categories seeded.`);

    console.log('Seeding products with randomized creation dates...');
    let productCount = 0;
    for (const [categoryName, productList] of Object.entries(products)) {
      const categoryId = categoryMap.get(categoryName);
      if (!categoryId) {
        console.warn(`⚠️  Category "${categoryName}" not found in map. Skipping products.`);
        continue;
      }

      for (const product of productList) {
        const price = product.price;
        if (!price) {
          console.warn(`⚠️ Product "${product.name}" is missing a price. Skipping.`);
          continue;
        }
        
        const salePrice = Math.random() > 0.7 ? (price * 0.8).toFixed(2) : null;
        const stock = Math.floor(Math.random() * 100);
        const createdAt = getRandomDate();

        const productRes = await client.query(
          `INSERT INTO products(name, description, price, sale_price, category_id, brand, material, dimensions, stock_quantity, min_stock_level, created_at)
           VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING id`,
          [product.name, product.description, price, salePrice, categoryId, product.brand, product.material, product.dimensions, stock, 5, createdAt]
        );
        const productId = productRes.rows[0].id;
        productCount++;

        if (product.images && product.images.length > 0) {
          for (let i = 0; i < product.images.length; i++) {
            await client.query(
              `INSERT INTO product_images(product_id, image_url, alt_text, is_primary) VALUES($1, $2, $3, $4)`,
              [productId, product.images[i], `Image of ${product.name}`, i === 0]
            );
          }
        }
      }
    }
    console.log(`${productCount} products seeded.`);

    await client.query('COMMIT');
    console.log('✅ Database seeded successfully!');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

seed();