import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instances for different services
const createAPIInstance = (baseURL: string) => {
  const instance = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor to handle common errors
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// API instances
export const authAPI = createAPIInstance(`${API_BASE_URL}/api`);
export const productAPI = createAPIInstance(`${API_BASE_URL}/api`);
export const cartAPI = createAPIInstance(`${API_BASE_URL}/api`);
export const orderAPI = createAPIInstance(`${API_BASE_URL}/api`);
export const aiAPI = createAPIInstance(`${API_BASE_URL}/api`);

// Auth API methods
export const authService = {
  login: (email: string, password: string) =>
    authAPI.post('/auth/login', { email, password }),
  
  register: (userData: any) =>
    authAPI.post('/auth/register', userData),
  
  logout: () =>
    authAPI.post('/auth/logout'),
  
  getProfile: () =>
    authAPI.get('/auth/me'),
  
  changePassword: (currentPassword: string, newPassword: string) =>
    authAPI.post('/auth/change-password', { currentPassword, newPassword }),
};

// Product API methods
export const productService = {
  getProducts: (params?: any) =>
    productAPI.get('/products', { params }),
  
  getProduct: (id: number) =>
    productAPI.get(`/products/${id}`),
  
  getCategories: (includeProducts = false) =>
    productAPI.get('/categories', { params: { include_products: includeProducts } }),
  
  getCategory: (id: number) =>
    productAPI.get(`/categories/${id}`),
  
  searchProducts: (params: any) =>
    productAPI.get('/search', { params }),
  
  getSearchSuggestions: (query: string) =>
    productAPI.get('/search/suggestions', { params: { q: query } }),
  
  getSimilarProducts: (id: number, limit = 10) =>
    productAPI.get(`/search/similar/${id}`, { params: { limit } }),
  
  getPopularProducts: (categoryId?: number, limit = 20) =>
    productAPI.get('/search/popular', { params: { category_id: categoryId, limit } }),
};

// Cart API methods
export const cartService = {
  getCart: () =>
    cartAPI.get('/cart'),
  
  addToCart: (productId: number, quantity: number) =>
    cartAPI.post('/cart/items', { product_id: productId, quantity }),
  
  updateCartItem: (itemId: number, quantity: number) =>
    cartAPI.put(`/cart/items/${itemId}`, { quantity }),
  
  removeFromCart: (itemId: number) =>
    cartAPI.delete(`/cart/items/${itemId}`),
  
  clearCart: () =>
    cartAPI.delete('/cart'),
  
  getCartCount: () =>
    cartAPI.get('/cart/count'),
};

// Order API methods
export const orderService = {
  getOrders: (params?: any) =>
    orderAPI.get('/orders', { params }),
  
  getOrder: (id: number) =>
    orderAPI.get(`/orders/${id}`),
  
  createOrder: (orderData: any) =>
    orderAPI.post('/orders', orderData),
  
  cancelOrder: (id: number) =>
    orderAPI.delete(`/orders/${id}`),
  
  updateOrderStatus: (id: number, status: string, notes?: string) =>
    orderAPI.put(`/orders/${id}/status`, { status, notes }),
};

// Payment API methods
export const paymentService = {
  getPayments: (params?: any) =>
    orderAPI.get('/payments', { params }),
  
  getPayment: (id: number) =>
    orderAPI.get(`/payments/${id}`),
  
  processPayment: (paymentData: any) =>
    orderAPI.post('/payments', paymentData),
  
  updatePaymentStatus: (id: number, status: string, transactionId?: string, notes?: string) =>
    orderAPI.put(`/payments/${id}/status`, { status, transaction_id: transactionId, notes }),
};

// AI API methods
export const aiService = {
  detectObjects: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return aiAPI.post('/ai/detect', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  detectObjectsSlice: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return aiAPI.post('/ai/detect-slice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  detectObjectsSuperSlice: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return aiAPI.post('/ai/detect-super-slice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  getDetectionHistory: (params?: any) =>
    authAPI.get('/profile/detection-history', { params }),
  
  getRecommendations: (params?: any) =>
    authAPI.get('/profile/recommendations', { params }),
  
  markRecommendationViewed: (id: number) =>
    authAPI.put(`/profile/recommendations/${id}/viewed`),
};

// Profile API methods
export const profileService = {
  getProfile: () =>
    authAPI.get('/profile'),
  
  updateProfile: (profileData: any) =>
    authAPI.put('/profile', profileData),
  
  getOrders: (params?: any) =>
    authAPI.get('/profile/orders', { params }),
  
  getDetectionHistory: (params?: any) =>
    authAPI.get('/profile/detection-history', { params }),
  
  getRecommendations: (params?: any) =>
    authAPI.get('/profile/recommendations', { params }),
};

export default {
  auth: authService,
  product: productService,
  cart: cartService,
  order: orderService,
  payment: paymentService,
  ai: aiService,
  profile: profileService,
};
