#!/bin/bash

# Furniture Store Startup Script
echo "🏠 Starting Furniture Store with AI Detection..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your configuration before running again."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p ai-service/uploads
mkdir -p ai-service/models
mkdir -p logs

# Setup AI models
echo "🤖 Setting up AI models..."
chmod +x scripts/setup-models.sh
./scripts/setup-models.sh

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Initialize database
echo "🗄️  Initializing database..."
docker-compose exec postgres psql -U admin -d furniture_store -f /docker-entrypoint-initdb.d/schema.sql || true

# Show service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Furniture Store is starting up!"
echo ""
echo "📱 Services:"
echo "   • Frontend:      http://localhost:3000"
echo "   • API Gateway:   http://localhost:8000"
echo "   • AI Service:    http://localhost:8001"
echo "   • Product API:   http://localhost:8002"
echo "   • User API:      http://localhost:8003"
echo "   • Order API:     http://localhost:8004"
echo ""
echo "📊 Monitoring:"
echo "   • Prometheus:    http://localhost:9090"
echo "   • Grafana:       http://localhost:3001 (admin/admin)"
echo "   • RabbitMQ:      http://localhost:15672 (admin/password123)"
echo ""
echo "🗄️  Database:"
echo "   • PostgreSQL:    localhost:5432 (admin/password123)"
echo "   • Redis:         localhost:6379"
echo ""
echo "📝 Logs: docker-compose logs -f [service-name]"
echo "🛑 Stop: docker-compose down"
echo ""
echo "✅ Setup complete! Please wait a few more seconds for all services to fully initialize."
