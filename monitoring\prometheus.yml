global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'product-service'
    static_configs:
      - targets: ['product-service:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:8003']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'order-service'
    static_configs:
      - targets: ['order-service:8004']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15672']
    metrics_path: '/metrics'
    scrape_interval: 30s
