const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const updateProfileSchema = Joi.object({
  first_name: Joi.string().max(50),
  last_name: Joi.string().max(50),
  phone: Joi.string().pattern(/^[0-9+\-\s()]+$/).allow(''),
  address: Joi.string().allow('')
});

// GET /api/profile - <PERSON><PERSON>y thông tin profile của user hiện tại
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const query = `
      SELECT id, username, email, first_name, last_name, phone, address, 
             role, is_active, created_at, updated_at
      FROM users 
      WHERE id = $1 AND is_active = true
    `;

    const result = await pool.query(query, [userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = result.rows[0];

    // Get additional profile statistics
    const statsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE user_id = $1) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE user_id = $1 AND status = 'completed') as completed_orders,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = $1 AND status = 'completed') as total_spent,
        (SELECT COUNT(*) FROM detection_history WHERE user_id = $1) as ai_detections_used
    `;

    const statsResult = await pool.query(statsQuery, [userId]);
    const stats = statsResult.rows[0];

    // Convert numeric strings to numbers
    stats.total_orders = parseInt(stats.total_orders);
    stats.completed_orders = parseInt(stats.completed_orders);
    stats.total_spent = parseFloat(stats.total_spent);
    stats.ai_detections_used = parseInt(stats.ai_detections_used);

    res.json({
      user,
      stats
    });

  } catch (error) {
    console.error('Error fetching profile:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/profile - Cập nhật thông tin profile
router.put('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { error, value } = updateProfileSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    Object.keys(value).forEach(key => {
      updateFields.push(`${key} = $${paramIndex}`);
      updateValues.push(value[key]);
      paramIndex++;
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(userId);

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND is_active = true
      RETURNING id, username, email, first_name, last_name, phone, address, 
                role, is_active, created_at, updated_at
    `;

    const result = await pool.query(query, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ 
      message: 'Profile updated successfully',
      user: result.rows[0] 
    });

  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/profile/orders - Lấy lịch sử đơn hàng của user
router.get('/orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT o.id, o.order_number, o.status, o.total_amount, o.shipping_address,
             o.payment_method, o.payment_status, o.created_at, o.updated_at,
             COUNT(oi.id) as item_count
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.user_id = $1
    `;

    const queryParams = [userId];
    let paramIndex = 2;

    if (status) {
      query += ` AND o.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    query += ` GROUP BY o.id ORDER BY o.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM orders WHERE user_id = $1`;
    const countParams = [userId];

    if (status) {
      countQuery += ` AND status = $2`;
      countParams.push(status);
    }

    const [ordersResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const orders = ordersResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching user orders:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/profile/detection-history - Lấy lịch sử sử dụng AI detection
router.get('/detection-history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const query = `
      SELECT id, image_url, detected_objects_count, processing_time, method, created_at
      FROM detection_history
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const countQuery = `
      SELECT COUNT(*) as total FROM detection_history WHERE user_id = $1
    `;

    const [historyResult, countResult] = await Promise.all([
      pool.query(query, [userId, limit, offset]),
      pool.query(countQuery, [userId])
    ]);

    const history = historyResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      history,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching detection history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/profile/recommendations - Lấy gợi ý sản phẩm cho user
router.get('/recommendations', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { page = 1, limit = 10, viewed } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT pr.id, pr.detected_object_class, pr.confidence_score, 
             pr.recommendation_reason, pr.is_viewed, pr.is_purchased, pr.created_at,
             p.id as product_id, p.name as product_name, p.price, p.sale_price,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as product_image
      FROM product_recommendations pr
      JOIN products p ON pr.product_id = p.id
      WHERE pr.user_id = $1 AND p.is_active = true
    `;

    const queryParams = [userId];
    let paramIndex = 2;

    if (viewed !== undefined) {
      query += ` AND pr.is_viewed = $${paramIndex}`;
      queryParams.push(viewed === 'true');
      paramIndex++;
    }

    query += ` ORDER BY pr.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM product_recommendations pr
      JOIN products p ON pr.product_id = p.id
      WHERE pr.user_id = $1 AND p.is_active = true
    `;
    const countParams = [userId];

    if (viewed !== undefined) {
      countQuery += ` AND pr.is_viewed = $2`;
      countParams.push(viewed === 'true');
    }

    const [recommendationsResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const recommendations = recommendationsResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      recommendations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/profile/recommendations/:id/viewed - Đánh dấu gợi ý đã xem
router.put('/recommendations/:id/viewed', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    const query = `
      UPDATE product_recommendations 
      SET is_viewed = true
      WHERE id = $1 AND user_id = $2
      RETURNING id
    `;

    const result = await pool.query(query, [id, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Recommendation not found' });
    }

    res.json({ message: 'Recommendation marked as viewed' });

  } catch (error) {
    console.error('Error marking recommendation as viewed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
