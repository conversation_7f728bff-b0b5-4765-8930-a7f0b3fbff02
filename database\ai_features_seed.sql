-- AI Features Seed Data
-- Insert sample data for AI-related tables

-- Insert object-product mappings
INSERT INTO object_product_mappings (object_class, product_category_id, mapping_weight, keywords) VALUES
-- Seating furniture
('chair', 1, 1.0, ARRAY['chair', 'seat', 'seating', 'armchair', 'dining chair', 'office chair']),
('sofa', 1, 1.0, AR<PERSON><PERSON>['sofa', 'couch', 'sectional', 'loveseat', 'living room']),
('bench', 1, 0.8, ARRAY['bench', 'seating', 'entryway', 'hallway']),
('stool', 1, 0.8, ARRAY['stool', 'bar stool', 'counter stool', 'high chair']),

-- Tables
('table', 2, 1.0, ARRAY['table', 'dining table', 'coffee table', 'side table', 'end table']),
('desk', 2, 1.0, ARRAY['desk', 'office desk', 'computer desk', 'workstation', 'study desk']),

-- Storage furniture
('cabinet', 3, 1.0, ARRA<PERSON>['cabinet', 'storage', 'cupboard', 'armoire']),
('shelf', 3, 0.9, ARRAY['shelf', 'bookcase', 'bookshelf', 'display unit', 'storage unit']),
('drawer', 3, 0.7, ARRAY['drawer', 'chest of drawers', 'dresser', 'storage']),
('dresser', 3, 1.0, ARRAY['dresser', 'chest of drawers', 'bedroom storage']),

-- Bedroom furniture
('bed', 4, 1.0, ARRAY['bed', 'bed frame', 'mattress', 'bedroom', 'sleeping']),
('pillow', 4, 0.6, ARRAY['pillow', 'cushion', 'throw pillow', 'bedding', 'soft furnishing']),

-- Lighting
('lamp', 5, 0.8, ARRAY['lamp', 'lighting', 'table lamp', 'floor lamp', 'desk lamp']),

-- Decor and accessories
('mirror', 6, 0.7, ARRAY['mirror', 'wall mirror', 'decorative mirror', 'bathroom mirror']),
('picture', 6, 0.5, ARRAY['picture', 'wall art', 'frame', 'artwork', 'painting', 'photo']),
('vase', 6, 0.4, ARRAY['vase', 'decorative vase', 'flower vase', 'home decor']),
('plant', 6, 0.3, ARRAY['plant', 'planter', 'pot', 'garden', 'indoor plant']),
('clock', 6, 0.4, ARRAY['clock', 'wall clock', 'table clock', 'timepiece']),

-- Textiles
('curtain', 7, 0.6, ARRAY['curtain', 'drape', 'window treatment', 'blind', 'shade']),
('rug', 7, 0.7, ARRAY['rug', 'carpet', 'area rug', 'floor covering', 'mat']),
('carpet', 7, 0.7, ARRAY['carpet', 'rug', 'floor covering', 'area carpet']),

-- Tableware and kitchen
('plate', 8, 0.3, ARRAY['plate', 'dish', 'dinnerware', 'tableware', 'kitchen']),
('cup', 8, 0.3, ARRAY['cup', 'mug', 'glass', 'drinkware', 'tableware']),
('bowl', 8, 0.3, ARRAY['bowl', 'serving bowl', 'dinnerware', 'tableware']),

-- Books and media
('book', 9, 0.2, ARRAY['book', 'books', 'reading', 'literature', 'home office']);

-- Insert sample AI model performance data
INSERT INTO ai_model_performance (
    model_name, 
    model_version, 
    detection_method, 
    total_detections, 
    avg_confidence, 
    avg_processing_time, 
    success_rate, 
    furniture_objects_detected, 
    small_objects_detected,
    date_recorded
) VALUES
('yolo11s', '1.0', 'standard', 1500, 0.75, 2.3, 0.85, 1200, 300, CURRENT_DATE - INTERVAL '7 days'),
('yolo11s', '1.0', 'slice', 2000, 0.68, 4.1, 0.88, 1600, 400, CURRENT_DATE - INTERVAL '6 days'),
('yolo11s', '1.0', 'super_slice', 2500, 0.62, 6.8, 0.92, 2000, 500, CURRENT_DATE - INTERVAL '5 days'),
('furniture_finetuned', '1.0', 'standard', 1800, 0.82, 2.1, 0.91, 1650, 150, CURRENT_DATE - INTERVAL '4 days'),
('furniture_finetuned', '1.0', 'slice', 2200, 0.78, 3.9, 0.93, 2000, 200, CURRENT_DATE - INTERVAL '3 days'),
('furniture_finetuned', '1.0', 'super_slice', 2800, 0.74, 6.2, 0.95, 2500, 300, CURRENT_DATE - INTERVAL '2 days'),
('small_objects', '1.0', 'standard', 1200, 0.65, 2.5, 0.78, 800, 400, CURRENT_DATE - INTERVAL '1 day'),
('small_objects', '1.0', 'slice', 1600, 0.71, 4.3, 0.83, 1000, 600, CURRENT_DATE),
('small_objects', '1.0', 'super_slice', 2000, 0.68, 7.1, 0.87, 1200, 800, CURRENT_DATE);

-- Insert sample detection sessions (for testing)
INSERT INTO ai_detection_sessions (
    session_id,
    user_id,
    image_url,
    image_size_width,
    image_size_height,
    detection_method,
    model_used,
    processing_time,
    confidence_threshold
) VALUES
('session_001', 1, '/uploads/sample_living_room.jpg', 1920, 1080, 'standard', 'furniture_finetuned', 2.1, 0.25),
('session_002', 1, '/uploads/sample_bedroom.jpg', 1600, 1200, 'slice', 'furniture_finetuned', 3.8, 0.25),
('session_003', 2, '/uploads/sample_kitchen.jpg', 1280, 960, 'super_slice', 'small_objects', 6.5, 0.20),
('session_004', 2, '/uploads/sample_office.jpg', 1920, 1080, 'standard', 'yolo11s', 2.3, 0.30);

-- Insert sample detected objects
INSERT INTO detected_objects (
    session_id,
    object_class,
    confidence,
    bbox_x,
    bbox_y,
    bbox_width,
    bbox_height,
    furniture_category
) VALUES
-- Living room objects (session_001)
('session_001', 'sofa', 0.89, 200, 300, 600, 400, 'Sofas and Couches'),
('session_001', 'table', 0.76, 400, 600, 300, 200, 'Tables'),
('session_001', 'lamp', 0.68, 100, 200, 80, 150, 'Lighting'),
('session_001', 'pillow', 0.45, 250, 320, 60, 40, 'Pillows and Cushions'),
('session_001', 'rug', 0.72, 150, 650, 500, 300, 'Floor Coverings'),

-- Bedroom objects (session_002)
('session_002', 'bed', 0.92, 300, 200, 800, 600, 'Beds'),
('session_002', 'dresser', 0.84, 100, 400, 200, 150, 'Dressers'),
('session_002', 'mirror', 0.67, 120, 300, 80, 120, 'Mirrors'),
('session_002', 'pillow', 0.52, 400, 250, 50, 30, 'Pillows and Cushions'),
('session_002', 'curtain', 0.71, 50, 50, 100, 400, 'Window Treatments'),

-- Kitchen objects (session_003)
('session_003', 'cabinet', 0.88, 50, 100, 300, 400, 'Cabinets and Storage'),
('session_003', 'chair', 0.79, 400, 500, 120, 180, 'Chairs and Seating'),
('session_003', 'table', 0.82, 350, 450, 200, 150, 'Tables'),
('session_003', 'plate', 0.34, 420, 480, 25, 25, 'Dinnerware'),
('session_003', 'cup', 0.28, 450, 485, 15, 20, 'Drinkware'),

-- Office objects (session_004)
('session_004', 'desk', 0.91, 200, 400, 400, 200, 'Desks'),
('session_004', 'chair', 0.85, 300, 300, 120, 180, 'Chairs and Seating'),
('session_004', 'shelf', 0.77, 100, 100, 150, 300, 'Shelves'),
('session_004', 'book', 0.41, 120, 150, 30, 20, 'Books and Media'),
('session_004', 'lamp', 0.63, 250, 380, 60, 100, 'Lighting');

-- Insert sample search queries
INSERT INTO ai_search_queries (
    session_id,
    user_id,
    search_type,
    detected_objects_count,
    search_query,
    results_count,
    avg_relevance_score,
    search_duration_ms
) VALUES
('session_001', 1, 'single_object', 1, 'sofa', 15, 0.82, 150),
('session_001', 1, 'multiple_objects', 5, 'sofa table lamp pillow rug', 25, 0.74, 280),
('session_002', 1, 'single_object', 1, 'bed', 12, 0.89, 120),
('session_003', 2, 'multiple_objects', 5, 'cabinet chair table plate cup', 18, 0.65, 320),
('session_004', 2, 'category_based', 4, 'office furniture', 22, 0.78, 200);

-- Insert sample product recommendations
INSERT INTO product_recommendations (
    user_id,
    product_id,
    detected_object_class,
    confidence_score,
    relevance_score,
    recommendation_reason,
    recommendation_method,
    detection_session_id
) VALUES
(1, 1, 'sofa', 0.89, 0.92, 'High confidence sofa detection with excellent category match', 'ai_detection', 'session_001'),
(1, 2, 'sofa', 0.89, 0.85, 'Similar style sofa based on detected object', 'ai_detection', 'session_001'),
(1, 3, 'table', 0.76, 0.78, 'Coffee table matching detected furniture', 'ai_detection', 'session_001'),
(1, 4, 'bed', 0.92, 0.94, 'Perfect bed match with high confidence', 'ai_detection', 'session_002'),
(2, 5, 'chair', 0.79, 0.81, 'Kitchen chair matching detected seating', 'ai_detection', 'session_003'),
(2, 6, 'desk', 0.91, 0.88, 'Office desk with high detection confidence', 'ai_detection', 'session_004');

-- Insert sample user interactions
INSERT INTO user_ai_interactions (
    user_id,
    session_id,
    interaction_type,
    object_class,
    product_id,
    interaction_data
) VALUES
(1, 'session_001', 'object_click', 'sofa', NULL, '{"bbox": [200, 300, 600, 400], "confidence": 0.89}'),
(1, 'session_001', 'search_trigger', 'sofa', NULL, '{"search_type": "single_object", "results_count": 15}'),
(1, 'session_001', 'product_view', 'sofa', 1, '{"relevance_score": 0.92, "view_duration": 45}'),
(1, 'session_001', 'product_click', 'sofa', 1, '{"relevance_score": 0.92, "click_position": "primary_image"}'),
(2, 'session_003', 'object_click', 'chair', NULL, '{"bbox": [400, 500, 120, 180], "confidence": 0.79}'),
(2, 'session_004', 'search_trigger', 'desk', NULL, '{"search_type": "category_based", "results_count": 22}');

-- Create views for analytics
CREATE OR REPLACE VIEW ai_detection_analytics AS
SELECT 
    DATE(ads.created_at) as detection_date,
    ads.model_used,
    ads.detection_method,
    COUNT(*) as total_sessions,
    AVG(ads.total_objects_detected) as avg_objects_per_session,
    AVG(ads.processing_time) as avg_processing_time,
    COUNT(DISTINCT ads.user_id) as unique_users
FROM ai_detection_sessions ads
GROUP BY DATE(ads.created_at), ads.model_used, ads.detection_method
ORDER BY detection_date DESC;

CREATE OR REPLACE VIEW object_detection_stats AS
SELECT 
    do.object_class,
    COUNT(*) as total_detections,
    AVG(do.confidence) as avg_confidence,
    MIN(do.confidence) as min_confidence,
    MAX(do.confidence) as max_confidence,
    AVG(do.object_area) as avg_object_area,
    COUNT(DISTINCT do.session_id) as unique_sessions
FROM detected_objects do
GROUP BY do.object_class
ORDER BY total_detections DESC;

CREATE OR REPLACE VIEW ai_search_performance AS
SELECT 
    asq.search_type,
    COUNT(*) as total_searches,
    AVG(asq.results_count) as avg_results_count,
    AVG(asq.avg_relevance_score) as avg_relevance_score,
    AVG(asq.search_duration_ms) as avg_search_duration_ms,
    COUNT(DISTINCT asq.user_id) as unique_users
FROM ai_search_queries asq
GROUP BY asq.search_type
ORDER BY total_searches DESC;
