const { pool } = require('../config/database');

/**
 * AI Product Matcher Service
 * Maps detected objects from AI service to product categories and finds similar products
 */
class AIProductMatcher {
    constructor() {
        this.objectToProductMapping = this.initializeMapping();
        this.confidenceWeights = this.initializeConfidenceWeights();
    }

    /**
     * Initialize mapping between detected object classes and product categories
     */
    initializeMapping() {
        return {
            // Seating furniture
            'chair': {
                categories: ['Chairs', 'Dining Chairs', 'Office Chairs', 'Armchairs'],
                keywords: ['chair', 'seat', 'seating', 'armchair', 'dining chair', 'office chair'],
                priority: 1.0
            },
            'sofa': {
                categories: ['Sofas', 'Couches', 'Sectionals', 'Loveseats'],
                keywords: ['sofa', 'couch', 'sectional', 'loveseat', 'living room'],
                priority: 1.0
            },
            'bench': {
                categories: ['Benches', 'Seating', 'Entryway Furniture'],
                keywords: ['bench', 'seating', 'entryway', 'hallway'],
                priority: 0.8
            },
            'stool': {
                categories: ['Stools', 'Bar Stools', 'Counter Stools'],
                keywords: ['stool', 'bar stool', 'counter stool', 'high chair'],
                priority: 0.8
            },

            // Tables
            'table': {
                categories: ['Tables', 'Dining Tables', 'Coffee Tables', 'Side Tables'],
                keywords: ['table', 'dining table', 'coffee table', 'side table', 'end table'],
                priority: 1.0
            },
            'desk': {
                categories: ['Desks', 'Office Furniture', 'Computer Desks'],
                keywords: ['desk', 'office desk', 'computer desk', 'workstation', 'study desk'],
                priority: 1.0
            },

            // Storage furniture
            'cabinet': {
                categories: ['Cabinets', 'Storage', 'Kitchen Cabinets', 'Bathroom Cabinets'],
                keywords: ['cabinet', 'storage', 'cupboard', 'armoire'],
                priority: 1.0
            },
            'shelf': {
                categories: ['Shelves', 'Bookcases', 'Display Units', 'Storage'],
                keywords: ['shelf', 'bookcase', 'bookshelf', 'display unit', 'storage unit'],
                priority: 0.9
            },
            'drawer': {
                categories: ['Dressers', 'Chest of Drawers', 'Storage'],
                keywords: ['drawer', 'chest of drawers', 'dresser', 'storage'],
                priority: 0.7
            },
            'dresser': {
                categories: ['Dressers', 'Bedroom Furniture', 'Chest of Drawers'],
                keywords: ['dresser', 'chest of drawers', 'bedroom storage'],
                priority: 1.0
            },

            // Bedroom furniture
            'bed': {
                categories: ['Beds', 'Bedroom Furniture', 'Bed Frames', 'Mattresses'],
                keywords: ['bed', 'bed frame', 'mattress', 'bedroom', 'sleeping'],
                priority: 1.0
            },
            'pillow': {
                categories: ['Pillows', 'Bedding', 'Cushions', 'Home Textiles'],
                keywords: ['pillow', 'cushion', 'throw pillow', 'bedding', 'soft furnishing'],
                priority: 0.6
            },

            // Lighting
            'lamp': {
                categories: ['Lamps', 'Lighting', 'Table Lamps', 'Floor Lamps'],
                keywords: ['lamp', 'lighting', 'table lamp', 'floor lamp', 'desk lamp'],
                priority: 0.8
            },

            // Decor and accessories
            'mirror': {
                categories: ['Mirrors', 'Wall Decor', 'Bathroom Accessories'],
                keywords: ['mirror', 'wall mirror', 'decorative mirror', 'bathroom mirror'],
                priority: 0.7
            },
            'picture': {
                categories: ['Wall Art', 'Frames', 'Decor', 'Artwork'],
                keywords: ['picture', 'wall art', 'frame', 'artwork', 'painting', 'photo'],
                priority: 0.5
            },
            'vase': {
                categories: ['Vases', 'Decor', 'Home Accessories'],
                keywords: ['vase', 'decorative vase', 'flower vase', 'home decor'],
                priority: 0.4
            },
            'plant': {
                categories: ['Plants', 'Planters', 'Garden', 'Home Decor'],
                keywords: ['plant', 'planter', 'pot', 'garden', 'indoor plant'],
                priority: 0.3
            },
            'clock': {
                categories: ['Clocks', 'Wall Decor', 'Home Accessories'],
                keywords: ['clock', 'wall clock', 'table clock', 'timepiece'],
                priority: 0.4
            },

            // Textiles
            'curtain': {
                categories: ['Curtains', 'Window Treatments', 'Home Textiles'],
                keywords: ['curtain', 'drape', 'window treatment', 'blind', 'shade'],
                priority: 0.6
            },
            'rug': {
                categories: ['Rugs', 'Carpets', 'Floor Coverings', 'Home Textiles'],
                keywords: ['rug', 'carpet', 'area rug', 'floor covering', 'mat'],
                priority: 0.7
            },
            'carpet': {
                categories: ['Carpets', 'Rugs', 'Floor Coverings'],
                keywords: ['carpet', 'rug', 'floor covering', 'area carpet'],
                priority: 0.7
            },

            // Tableware and kitchen
            'plate': {
                categories: ['Dinnerware', 'Plates', 'Kitchen', 'Tableware'],
                keywords: ['plate', 'dish', 'dinnerware', 'tableware', 'kitchen'],
                priority: 0.3
            },
            'cup': {
                categories: ['Drinkware', 'Cups', 'Mugs', 'Kitchen', 'Tableware'],
                keywords: ['cup', 'mug', 'glass', 'drinkware', 'tableware'],
                priority: 0.3
            },
            'bowl': {
                categories: ['Bowls', 'Dinnerware', 'Kitchen', 'Tableware'],
                keywords: ['bowl', 'serving bowl', 'dinnerware', 'tableware'],
                priority: 0.3
            },

            // Books and media
            'book': {
                categories: ['Books', 'Media', 'Home Office', 'Decor'],
                keywords: ['book', 'books', 'reading', 'literature', 'home office'],
                priority: 0.2
            }
        };
    }

    /**
     * Initialize confidence weights for different object types
     */
    initializeConfidenceWeights() {
        return {
            'high_confidence': { min: 0.7, weight: 1.0 },
            'medium_confidence': { min: 0.4, weight: 0.8 },
            'low_confidence': { min: 0.2, weight: 0.5 }
        };
    }

    /**
     * Find products based on detected objects
     */
    async findProductsForDetectedObjects(detectedObjects, options = {}) {
        const {
            limit = 20,
            includeCategories = true,
            minConfidence = 0.2,
            sortBy = 'relevance'
        } = options;

        try {
            // Filter objects by confidence
            const validObjects = detectedObjects.filter(obj => obj.confidence >= minConfidence);
            
            if (validObjects.length === 0) {
                return { products: [], categories: [], searchQueries: [] };
            }

            // Generate search queries and categories
            const searchData = this.generateSearchQueries(validObjects);
            
            // Search for products
            const products = await this.searchProducts(searchData, limit, sortBy);
            
            // Get relevant categories if requested
            const categories = includeCategories ? await this.getRelevantCategories(searchData) : [];

            return {
                products,
                categories,
                searchQueries: searchData.queries,
                detectedObjectsCount: validObjects.length,
                totalConfidence: this.calculateTotalConfidence(validObjects)
            };

        } catch (error) {
            console.error('Error finding products for detected objects:', error);
            throw error;
        }
    }

    /**
     * Generate search queries based on detected objects
     */
    generateSearchQueries(detectedObjects) {
        const queries = [];
        const categories = new Set();
        const keywords = new Set();

        detectedObjects.forEach(obj => {
            const mapping = this.objectToProductMapping[obj.class_name];
            if (mapping) {
                // Add categories
                mapping.categories.forEach(cat => categories.add(cat));
                
                // Add keywords with confidence weighting
                const confidenceWeight = this.getConfidenceWeight(obj.confidence);
                mapping.keywords.forEach(keyword => {
                    keywords.add(keyword);
                    queries.push({
                        text: keyword,
                        weight: mapping.priority * confidenceWeight,
                        source: obj.class_name,
                        confidence: obj.confidence
                    });
                });
            } else {
                // Fallback: use class name directly
                queries.push({
                    text: obj.class_name,
                    weight: this.getConfidenceWeight(obj.confidence),
                    source: obj.class_name,
                    confidence: obj.confidence
                });
            }
        });

        return {
            queries: queries.sort((a, b) => b.weight - a.weight),
            categories: Array.from(categories),
            keywords: Array.from(keywords)
        };
    }

    /**
     * Get confidence weight based on confidence score
     */
    getConfidenceWeight(confidence) {
        if (confidence >= this.confidenceWeights.high_confidence.min) {
            return this.confidenceWeights.high_confidence.weight;
        } else if (confidence >= this.confidenceWeights.medium_confidence.min) {
            return this.confidenceWeights.medium_confidence.weight;
        } else {
            return this.confidenceWeights.low_confidence.weight;
        }
    }

    /**
     * Search for products using generated queries
     */
    async searchProducts(searchData, limit, sortBy) {
        if (searchData.queries.length === 0) {
            return [];
        }

        // Build search query
        const searchTerms = searchData.queries.slice(0, 5).map(q => q.text); // Top 5 terms
        const categoryFilter = searchData.categories.slice(0, 3); // Top 3 categories

        let query = `
            SELECT DISTINCT p.*, c.name as category_name,
                   (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image,
                   (
                       CASE 
                           WHEN c.name = ANY($2) THEN 3
                           WHEN p.name ILIKE ANY($3) THEN 2
                           WHEN p.description ILIKE ANY($3) THEN 1
                           ELSE 0
                       END +
                       ts_rank(to_tsvector('english', p.name || ' ' || COALESCE(p.description, '')), 
                               plainto_tsquery('english', $1))
                   ) as relevance_score
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = true
            AND (
                to_tsvector('english', p.name || ' ' || COALESCE(p.description, '')) 
                @@ plainto_tsquery('english', $1)
                OR p.name ILIKE ANY($3)
                OR p.description ILIKE ANY($3)
                OR c.name = ANY($2)
            )
        `;

        // Add sorting
        if (sortBy === 'relevance') {
            query += ` ORDER BY relevance_score DESC, p.created_at DESC`;
        } else if (sortBy === 'price_asc') {
            query += ` ORDER BY p.price ASC, relevance_score DESC`;
        } else if (sortBy === 'price_desc') {
            query += ` ORDER BY p.price DESC, relevance_score DESC`;
        } else {
            query += ` ORDER BY p.created_at DESC, relevance_score DESC`;
        }

        query += ` LIMIT $4`;

        const searchText = searchTerms.join(' ');
        const likePatterns = searchTerms.map(term => `%${term}%`);

        const result = await pool.query(query, [
            searchText,
            categoryFilter,
            likePatterns,
            limit
        ]);

        return result.rows.map(p => ({
            ...p,
            price: p.price !== null ? Number(p.price) : p.price,
            sale_price: p.sale_price !== null ? Number(p.sale_price) : p.sale_price
        }));
    }

    /**
     * Get relevant categories based on search data
     */
    async getRelevantCategories(searchData) {
        if (searchData.categories.length === 0) {
            return [];
        }

        const query = `
            SELECT DISTINCT c.*, COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
            WHERE c.name = ANY($1) AND c.is_active = true
            GROUP BY c.id, c.name, c.description, c.image_url
            ORDER BY product_count DESC
            LIMIT 10
        `;

        const result = await pool.query(query, [searchData.categories]);
        return result.rows;
    }

    /**
     * Calculate total confidence score for detected objects
     */
    calculateTotalConfidence(detectedObjects) {
        if (detectedObjects.length === 0) return 0;
        
        const totalConfidence = detectedObjects.reduce((sum, obj) => sum + obj.confidence, 0);
        return totalConfidence / detectedObjects.length;
    }

    /**
     * Get product recommendations based on a single detected object
     */
    async getRecommendationsForObject(objectClass, confidence, limit = 10) {
        const mockDetectedObjects = [{
            class_name: objectClass,
            confidence: confidence
        }];

        return await this.findProductsForDetectedObjects(mockDetectedObjects, { limit });
    }
}

module.exports = AIProductMatcher;
