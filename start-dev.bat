@echo off
echo 🔧 Starting Furniture Store in Development Mode...

REM Check if dependencies are installed
if not exist web-frontend\node_modules (
    echo ❌ Frontend dependencies not found. Running npm run install:all first...
    call npm run install:all
)

echo.
echo 🚀 Starting development servers...
echo.
echo This will start:
echo   - Frontend (React): http://localhost:3000
echo   - API Gateway:      http://localhost:8000
echo   - User Service:     http://localhost:8003
echo   - Product Service:  http://localhost:8002
echo   - Order Service:    http://localhost:8004
echo   - AI Service:       http://localhost:8001
echo.
echo Note: You'll need PostgreSQL, Redis, and RabbitMQ running separately
echo      or use 'npm start' to run everything with Docker.
echo.

REM Install concurrently if not present
npm list concurrently >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing concurrently for parallel execution...
    npm install --save-dev concurrently
)

REM Start all services in development mode
npx concurrently ^
  --names "Frontend,Gateway,User,Product,Order,AI" ^
  --colors "cyan,yellow,green,blue,magenta,red" ^
  "cd web-frontend && npm start" ^
  "cd api-gateway && npm run dev" ^
  "cd user-service && npm run dev" ^
  "cd product-service && npm run dev" ^
  "cd order-service && npm run dev" ^
  "cd ai-service && python main.py"
