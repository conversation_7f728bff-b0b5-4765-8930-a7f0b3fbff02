# 🚀 Deployment Guide

Hướng dẫn triển khai hệ thống Furniture Store với AI Object Detection.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores (8+ cores khuyến nghị cho AI processing)
- **RAM**: 8GB minimum (16GB khuyến nghị)
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl (cho testing)

## 🏗️ Production Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

### 2. Application Deployment

```bash
# Clone repository
git clone <repository-url>
cd furniture-store

# Setup environment
cp .env.example .env
nano .env  # Edit production values

# Create production directories
sudo mkdir -p /var/lib/furniture-store/{postgres,grafana,uploads,models}
sudo chown -R $USER:$USER /var/lib/furniture-store

# Download AI models
curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt" -o ai-service/models/yolov8n.pt

# Start services
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Production Environment Variables

```bash
# Database
DATABASE_URL=************************************************/furniture_store
POSTGRES_PASSWORD=STRONG_PASSWORD

# Security
JWT_SECRET=VERY_STRONG_SECRET_KEY_HERE
CORS_ORIGIN=https://yourdomain.com

# Services
REACT_APP_API_URL=https://api.yourdomain.com

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Monitoring
GF_SECURITY_ADMIN_PASSWORD=STRONG_GRAFANA_PASSWORD
```

## 🔒 Security Configuration

### 1. SSL/TLS Setup

```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificates
sudo certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com

# Setup auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Firewall Configuration

```bash
# Setup UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000  # Frontend
sudo ufw allow 8000  # API Gateway
```

### 3. Nginx Reverse Proxy

```nginx
# /etc/nginx/sites-available/furniture-store
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

server {
    listen 443 ssl;
    server_name api.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 Monitoring Setup

### 1. Grafana Configuration

```bash
# Access Grafana
http://localhost:3001

# Default credentials: admin/admin
# Change password immediately

# Import dashboards:
# - Node Exporter Full
# - Docker Container Metrics
# - Custom Application Metrics
```

### 2. Prometheus Alerts

```yaml
# prometheus-alerts.yml
groups:
  - name: furniture-store
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
      
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
```

## 🔄 Backup Strategy

### 1. Database Backup

```bash
# Create backup script
cat > backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec postgres pg_dump -U admin furniture_store > backup_${DATE}.sql
aws s3 cp backup_${DATE}.sql s3://your-backup-bucket/
rm backup_${DATE}.sql
EOF

chmod +x backup-db.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup-db.sh
```

### 2. Application Data Backup

```bash
# Backup uploaded files and models
tar -czf app-data-$(date +%Y%m%d).tar.gz ai-service/uploads ai-service/models
aws s3 cp app-data-$(date +%Y%m%d).tar.gz s3://your-backup-bucket/
```

## 🔧 Maintenance

### 1. Log Rotation

```bash
# Setup logrotate
sudo nano /etc/logrotate.d/furniture-store

/var/lib/docker/containers/*/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
}
```

### 2. Health Checks

```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
services=("frontend:3000" "api-gateway:8000" "ai-service:8001" "product-service:8002" "user-service:8003" "order-service:8004")

for service in "${services[@]}"; do
    name=${service%:*}
    port=${service#*:}
    if curl -f http://localhost:$port/health > /dev/null 2>&1; then
        echo "✅ $name is healthy"
    else
        echo "❌ $name is down"
        # Send alert or restart service
    fi
done
EOF

chmod +x health-check.sh

# Run every 5 minutes
crontab -e
# Add: */5 * * * * /path/to/health-check.sh
```

## 🚨 Troubleshooting

### Common Production Issues

1. **Out of Memory**
```bash
# Check memory usage
docker stats
free -h

# Restart services if needed
docker-compose restart
```

2. **Disk Space**
```bash
# Clean up Docker
docker system prune -a

# Check disk usage
df -h
du -sh /var/lib/docker
```

3. **Service Not Responding**
```bash
# Check service logs
docker-compose logs service-name

# Restart specific service
docker-compose restart service-name
```

## 📈 Performance Optimization

### 1. Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_cart_items_user ON cart_items(user_id);
```

### 2. Redis Configuration

```bash
# Optimize Redis memory
echo "maxmemory 2gb" >> redis.conf
echo "maxmemory-policy allkeys-lru" >> redis.conf
```

### 3. AI Service Optimization

```python
# Use GPU if available
# Install CUDA and update requirements.txt
torch>=1.9.0+cu111
```

## 🔄 Updates and Rollbacks

### 1. Update Process

```bash
# Backup current state
docker-compose down
cp -r . ../furniture-store-backup

# Pull updates
git pull origin main

# Update services
docker-compose pull
docker-compose up -d
```

### 2. Rollback Process

```bash
# If update fails
docker-compose down
cp -r ../furniture-store-backup/* .
docker-compose up -d
```

## 📞 Support

- **Logs**: `docker-compose logs -f`
- **Health**: `./scripts/test.sh`
- **Monitoring**: http://localhost:3001
- **Documentation**: `/docs` directory
