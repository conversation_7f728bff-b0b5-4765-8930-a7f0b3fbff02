const express = require('express');
const router = express.Router();
const { pool, redisClient } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const addToCartSchema = Joi.object({
  product_id: Joi.number().integer().positive().required(),
  quantity: Joi.number().integer().min(1).required()
});

const updateCartItemSchema = Joi.object({
  quantity: Joi.number().integer().min(1).required()
});

// GET /api/cart - Lấy giỏ hàng của user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // Check cache first
    const cacheKey = `cart:${userId}`;
    const cachedCart = await redisClient.get(cacheKey);
    
    if (cachedCart) {
      return res.json(JSON.parse(cachedCart));
    }

    const query = `
      SELECT ci.id, ci.product_id, ci.quantity, ci.created_at,
             p.name, p.price, p.sale_price, p.stock_quantity,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as image_url,
             CASE 
               WHEN p.sale_price IS NOT NULL AND p.sale_price > 0 THEN p.sale_price
               ELSE p.price
             END as current_price
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      WHERE ci.user_id = $1 AND p.is_active = true
      ORDER BY ci.created_at DESC
    `;

    const result = await pool.query(query, [userId]);
    const items = result.rows;

    // Calculate totals
    let subtotal = 0;
    let totalItems = 0;

    items.forEach(item => {
      const itemTotal = parseFloat(item.current_price) * item.quantity;
      subtotal += itemTotal;
      totalItems += item.quantity;
      item.item_total = itemTotal;
    });

    const cart = {
      items,
      summary: {
        total_items: totalItems,
        subtotal: subtotal,
        tax: subtotal * 0.1, // 10% tax
        total: subtotal * 1.1
      }
    };

    // Cache for 5 minutes
    await redisClient.setEx(cacheKey, 300, JSON.stringify(cart));

    res.json(cart);

  } catch (error) {
    console.error('Error fetching cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/cart/items - Thêm sản phẩm vào giỏ hàng
router.post('/items', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { error, value } = addToCartSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { product_id, quantity } = value;

    // Check if product exists and is active
    const productQuery = await pool.query(
      'SELECT id, name, price, sale_price, stock_quantity FROM products WHERE id = $1 AND is_active = true',
      [product_id]
    );

    if (productQuery.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const product = productQuery.rows[0];

    // Check stock availability
    if (product.stock_quantity < quantity) {
      return res.status(400).json({ 
        error: 'Insufficient stock',
        available_quantity: product.stock_quantity
      });
    }

    // Check if item already exists in cart
    const existingItemQuery = await pool.query(
      'SELECT id, quantity FROM cart_items WHERE user_id = $1 AND product_id = $2',
      [userId, product_id]
    );

    let result;

    if (existingItemQuery.rows.length > 0) {
      // Update existing item
      const existingItem = existingItemQuery.rows[0];
      const newQuantity = existingItem.quantity + quantity;

      if (product.stock_quantity < newQuantity) {
        return res.status(400).json({ 
          error: 'Insufficient stock for total quantity',
          available_quantity: product.stock_quantity,
          current_cart_quantity: existingItem.quantity
        });
      }

      result = await pool.query(
        'UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
        [newQuantity, existingItem.id]
      );
    } else {
      // Add new item
      result = await pool.query(
        'INSERT INTO cart_items (user_id, product_id, quantity) VALUES ($1, $2, $3) RETURNING *',
        [userId, product_id, quantity]
      );
    }

    // Clear cart cache
    await redisClient.del(`cart:${userId}`);

    res.status(201).json({
      message: 'Item added to cart successfully',
      cart_item: result.rows[0]
    });

  } catch (error) {
    console.error('Error adding item to cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/cart/items/:id - Cập nhật số lượng sản phẩm trong giỏ hàng
router.put('/items/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;
    const { error, value } = updateCartItemSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { quantity } = value;

    // Get cart item with product info
    const itemQuery = `
      SELECT ci.id, ci.product_id, ci.quantity, p.stock_quantity
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      WHERE ci.id = $1 AND ci.user_id = $2 AND p.is_active = true
    `;

    const itemResult = await pool.query(itemQuery, [id, userId]);

    if (itemResult.rows.length === 0) {
      return res.status(404).json({ error: 'Cart item not found' });
    }

    const item = itemResult.rows[0];

    // Check stock availability
    if (item.stock_quantity < quantity) {
      return res.status(400).json({ 
        error: 'Insufficient stock',
        available_quantity: item.stock_quantity
      });
    }

    // Update quantity
    const result = await pool.query(
      'UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [quantity, id]
    );

    // Clear cart cache
    await redisClient.del(`cart:${userId}`);

    res.json({
      message: 'Cart item updated successfully',
      cart_item: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/cart/items/:id - Xóa sản phẩm khỏi giỏ hàng
router.delete('/items/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM cart_items WHERE id = $1 AND user_id = $2 RETURNING id',
      [id, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Cart item not found' });
    }

    // Clear cart cache
    await redisClient.del(`cart:${userId}`);

    res.json({ message: 'Item removed from cart successfully' });

  } catch (error) {
    console.error('Error removing cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/cart - Xóa toàn bộ giỏ hàng
router.delete('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    await pool.query('DELETE FROM cart_items WHERE user_id = $1', [userId]);

    // Clear cart cache
    await redisClient.del(`cart:${userId}`);

    res.json({ message: 'Cart cleared successfully' });

  } catch (error) {
    console.error('Error clearing cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/cart/count - Lấy số lượng items trong giỏ hàng
router.get('/count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const result = await pool.query(
      'SELECT COALESCE(SUM(quantity), 0) as total_items FROM cart_items WHERE user_id = $1',
      [userId]
    );

    const totalItems = parseInt(result.rows[0].total_items);

    res.json({ total_items: totalItems });

  } catch (error) {
    console.error('Error getting cart count:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
