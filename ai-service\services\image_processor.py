import cv2
import numpy as np
from PIL import Image
import logging
from pathlib import Path
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

class ImageProcessor:
    """
    Service xử lý ảnh trước khi nhận diện
    """
    
    def __init__(self):
        self.max_size = 1920  # Kích thước tối đa cho ảnh
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    def preprocess_image(self, image_path: str) -> str:
        """
        Tiền xử lý ảnh: resize, normalize, format conversion
        
        Args:
            image_path: Đường dẫn đến ảnh
            
        Returns:
            Đường dẫn đến ảnh đã được xử lý
        """
        try:
            # Đọc ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Không thể đọc ảnh: {image_path}")
            
            # Resize nếu ảnh quá lớn
            image = self._resize_if_needed(image)
            
            # <PERSON><PERSON><PERSON> thiện chất lượng ảnh
            image = self._enhance_image(image)
            
            # Lưu ảnh đã xử lý
            processed_path = self._get_processed_path(image_path)
            cv2.imwrite(processed_path, image)
            
            logger.info(f"Preprocessed image saved: {processed_path}")
            return processed_path
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            raise
    
    def _resize_if_needed(self, image: np.ndarray) -> np.ndarray:
        """
        Resize ảnh nếu kích thước quá lớn
        """
        height, width = image.shape[:2]
        
        if height > self.max_size or width > self.max_size:
            # Tính tỷ lệ scale
            scale = min(self.max_size / height, self.max_size / width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # Resize với interpolation tốt
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")
        
        return image
    
    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        Cải thiện chất lượng ảnh
        """
        # Chuyển sang LAB color space để cải thiện độ sáng
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Áp dụng CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge lại và chuyển về BGR
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # Giảm noise
        enhanced = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        return enhanced
    
    def _get_processed_path(self, original_path: str) -> str:
        """
        Tạo đường dẫn cho ảnh đã xử lý
        """
        path = Path(original_path)
        processed_name = f"{path.stem}_processed{path.suffix}"
        return str(path.parent / processed_name)
    
    def validate_image(self, image_path: str) -> bool:
        """
        Kiểm tra tính hợp lệ của ảnh
        """
        try:
            path = Path(image_path)
            
            # Kiểm tra file tồn tại
            if not path.exists():
                return False
            
            # Kiểm tra định dạng
            if path.suffix.lower() not in self.supported_formats:
                return False
            
            # Kiểm tra có thể đọc được
            image = cv2.imread(image_path)
            if image is None:
                return False
            
            # Kiểm tra kích thước hợp lý
            height, width = image.shape[:2]
            if height < 32 or width < 32:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating image: {e}")
            return False
    
    def get_image_info(self, image_path: str) -> dict:
        """
        Lấy thông tin về ảnh
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {}
            
            height, width, channels = image.shape
            file_size = Path(image_path).stat().st_size
            
            return {
                "width": width,
                "height": height,
                "channels": channels,
                "file_size": file_size,
                "format": Path(image_path).suffix.lower()
            }
            
        except Exception as e:
            logger.error(f"Error getting image info: {e}")
            return {}
    
    def create_thumbnail(self, image_path: str, size: Tuple[int, int] = (200, 200)) -> str:
        """
        Tạo thumbnail cho ảnh
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            # Resize giữ tỷ lệ
            height, width = image.shape[:2]
            aspect_ratio = width / height
            
            if aspect_ratio > 1:
                new_width = size[0]
                new_height = int(size[0] / aspect_ratio)
            else:
                new_height = size[1]
                new_width = int(size[1] * aspect_ratio)
            
            thumbnail = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            # Tạo đường dẫn thumbnail
            path = Path(image_path)
            thumbnail_name = f"{path.stem}_thumb{path.suffix}"
            thumbnail_path = str(path.parent / thumbnail_name)
            
            cv2.imwrite(thumbnail_path, thumbnail)
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            raise
