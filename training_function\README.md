## Training tutorial

**Bước 1:** <PERSON><PERSON> lý và chuẩn hóa dữ liệu

Mục tiêu: Biến đổi các bộ dữ liệu thô có định dạng khác nhau về cùng một định dạng  YOLO

### `a. run_baseline.py:` 
* **<PERSON><PERSON><PERSON> đích**: Tự động tải và thiết lập bộ dữ liệu HomeObjects-3K làm nền tảng.
* **Đầu vào**: Không cần.
* **Đầu ra**: Thư mục datasets/homeobjects-3K với định dạng YOLO chuẩn. 

### `b. convert_sunrgbd.py:` 
* **M<PERSON><PERSON> đích**: Đọc dữ liệu từ raw_data/sunrgbd (định dạng JSON), chuyển đổi annotation và lưu vào thư mục processed_data.
* **Đ<PERSON><PERSON> vào**: <PERSON><PERSON> liệu thô của SUNRGBD.
* **<PERSON><PERSON><PERSON> ra**: Ảnh và file label của SUNRGBD theo định dạng YOLO

### `c. convert_ade20k.py:` 
* **Mục đích**: Đọc dữ liệu từ raw_data/ade20k (định dạng Mask Segmentation), chuyển đổi mask thành bounding box YOLO và lưu vào thư mục processed_data.
* **Đầu vào**: Dữ liệu thô của ADE20K.
* **Đầu ra**: Ảnh và file label của ADE20K theo định dạng YOLO

**Bước 2:** Hợp nhất và Phân chia Dữ liệu Cuối cùng

Mục tiêu: Gộp tất cả dữ liệu chuẩn hóa vào 1 thư mục, và tạo các tập train/val cuối cùng

### a. `merge_data.py`
* **Mục đích:** Sao chép dữ liệu đã xử lý của **HomeObjects-3K** từ thư mục `datasets` vào thư mục chung `processed_data`.
* **Đầu vào:** Thư mục `datasets/homeobjects-3K`.
* **Đầu ra:** Dữ liệu của HomeObjects-3K được thêm vào `processed_data`.

### b. `create_val_set.py`
* **Mục đích:** Trộn và di chuyển ngẫu nhiên một phần dữ liệu từ tập `train` tổng hợp để tạo ra tập `validation` cuối cùng.
* **Đầu vào:** Thư mục `processed_data` đã được gộp.
* **Đầu ra:** Cấu trúc `processed_data` với hai tập `train` và `val` đã được phân chia hợp lý.

## Bước 3: Huấn luyện Mô hình

Mục tiêu: Sử dụng bộ dữ liệu đã hoàn thiện để huấn luyện mô hình AI.

### a. `furniture_data.yaml`
* **Mục đích:** File cấu hình "bản đồ", chỉ cho script huấn luyện biết dữ liệu nằm ở đâu, có bao nhiêu lớp, và tên của chúng là gì.

### b. `train_model.py`
* **Mục đích:** Script chính để khởi động quá trình huấn luyện mô hình YOLOv8.
* **Đầu vào:** File `furniture_data.yaml` và thư mục `processed_data`.
* **Đầu ra:** Thư mục `runs` chứa file model **`best.pt`**.