import os
import yaml
import json
import shutil
from pathlib import Path
from ultralytics import YOL<PERSON>
import torch
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FurnitureModelTrainer:
    """
    Advanced trainer for furniture detection models with HomeObjects-3K dataset
    """

    def __init__(self, config_path="training_config.yaml"):
        self.config = self.load_config(config_path)
        self.setup_directories()

    def load_config(self, config_path):
        """Load training configuration"""
        default_config = {
            'model': {
                'base_model': 'yolo11s.pt',
                'architecture': 'yolo11s',
                'input_size': 640
            },
            'training': {
                'epochs': 200,
                'batch_size': 16,
                'learning_rate': 0.001,
                'patience': 50,
                'save_period': 10
            },
            'data': {
                'dataset_path': 'datasets/HomeObjects-3K',
                'yaml_file': 'furniture_data.yaml',
                'train_split': 0.8,
                'val_split': 0.15,
                'test_split': 0.05
            },
            'augmentation': {
                'mosaic': 1.0,
                'mixup': 0.1,
                'copy_paste': 0.1,
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 0.0,
                'translate': 0.1,
                'scale': 0.5,
                'shear': 0.0,
                'perspective': 0.0,
                'flipud': 0.0,
                'fliplr': 0.5
            },
            'optimization': {
                'optimizer': 'AdamW',
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 3,
                'warmup_momentum': 0.8,
                'warmup_bias_lr': 0.1
            }
        }

        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                # Merge configs
                for key, value in user_config.items():
                    if isinstance(value, dict) and key in default_config:
                        default_config[key].update(value)
                    else:
                        default_config[key] = value

        return default_config

    def setup_directories(self):
        """Setup training directories"""
        self.runs_dir = Path("runs/train")
        self.runs_dir.mkdir(parents=True, exist_ok=True)

        # Create timestamped run directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.runs_dir / f"furniture_yolo11s_{timestamp}"
        self.run_dir.mkdir(exist_ok=True)

        logger.info(f"Training run directory: {self.run_dir}")

    def prepare_homeobjects_dataset(self):
        """
        Prepare HomeObjects-3K dataset for training
        """
        dataset_path = Path(self.config['data']['dataset_path'])

        if not dataset_path.exists():
            logger.error(f"Dataset path {dataset_path} does not exist!")
            raise FileNotFoundError(f"Dataset not found at {dataset_path}")

        # Create YOLO format dataset structure
        yolo_dataset_path = self.run_dir / "dataset"
        yolo_dataset_path.mkdir(exist_ok=True)

        for split in ['train', 'val', 'test']:
            (yolo_dataset_path / split / 'images').mkdir(parents=True, exist_ok=True)
            (yolo_dataset_path / split / 'labels').mkdir(parents=True, exist_ok=True)

        # Copy and organize dataset files
        self._organize_dataset_files(dataset_path, yolo_dataset_path)

        # Create data.yaml file
        self._create_data_yaml(yolo_dataset_path)

        return yolo_dataset_path

    def _organize_dataset_files(self, source_path, target_path):
        """Organize dataset files into YOLO format"""
        # This would contain logic to organize HomeObjects-3K dataset
        # into YOLO format with proper train/val/test splits
        logger.info("Organizing dataset files...")

        # Placeholder for dataset organization logic
        # In real implementation, this would:
        # 1. Read HomeObjects-3K annotations
        # 2. Convert to YOLO format
        # 3. Split into train/val/test
        # 4. Copy files to appropriate directories

    def _create_data_yaml(self, dataset_path):
        """Create data.yaml file for YOLO training"""
        # Define furniture classes for HomeObjects-3K
        furniture_classes = [
            'chair', 'table', 'sofa', 'bed', 'desk', 'cabinet', 'shelf',
            'lamp', 'pillow', 'plate', 'cup', 'bowl', 'vase', 'mirror',
            'curtain', 'rug', 'plant', 'book', 'clock', 'picture'
        ]

        data_config = {
            'path': str(dataset_path.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': len(furniture_classes),
            'names': furniture_classes
        }

        yaml_path = dataset_path / 'data.yaml'
        with open(yaml_path, 'w') as f:
            yaml.dump(data_config, f, default_flow_style=False)

        logger.info(f"Created data.yaml at {yaml_path}")
        return yaml_path

    def train_model(self):
        """
        Train the furniture detection model
        """
        logger.info("Starting furniture detection model training...")

        # Prepare dataset
        dataset_path = self.prepare_homeobjects_dataset()
        data_yaml = dataset_path / 'data.yaml'

        # Load base model
        model_config = self.config['model']
        model = YOLO(model_config['base_model'])

        # Configure training parameters
        train_config = self.config['training']

        # Start training
        results = model.train(
            data=str(data_yaml),
            epochs=train_config['epochs'],
            imgsz=model_config['input_size'],
            batch=train_config['batch_size'],
            lr0=train_config['learning_rate'],
            patience=train_config['patience'],
            save_period=train_config['save_period'],
            device='cuda' if torch.cuda.is_available() else 'cpu',
            project=str(self.runs_dir),
            name=f"furniture_yolo11s_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            exist_ok=True,
            pretrained=True,
            optimizer=self.config['optimization']['optimizer'],
            verbose=True,
            seed=42,
            deterministic=True,
            single_cls=False,
            rect=False,
            cos_lr=True,
            close_mosaic=10,
            resume=False,
            amp=True,
            fraction=1.0,
            profile=False,
            # Augmentation parameters
            **self.config['augmentation']
        )

        # Save training results
        self.save_training_results(results)

        # Evaluate model
        self.evaluate_model(model)

        return results

    def save_training_results(self, results):
        """Save training results and metrics"""
        results_path = self.run_dir / 'training_results.json'

        # Extract key metrics
        metrics = {
            'training_completed': True,
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'best_fitness': float(results.best_fitness) if hasattr(results, 'best_fitness') else None,
            'final_epoch': results.epoch if hasattr(results, 'epoch') else None
        }

        with open(results_path, 'w') as f:
            json.dump(metrics, f, indent=2)

        logger.info(f"Training results saved to {results_path}")

    def evaluate_model(self, model):
        """Evaluate the trained model"""
        logger.info("Evaluating trained model...")

        try:
            # Run validation
            val_results = model.val()

            # Log evaluation metrics
            if hasattr(val_results, 'box'):
                logger.info(f"mAP50: {val_results.box.map50:.4f}")
                logger.info(f"mAP50-95: {val_results.box.map:.4f}")

            # Save evaluation results
            eval_path = self.run_dir / 'evaluation_results.json'
            eval_metrics = {
                'evaluation_completed': True,
                'timestamp': datetime.now().isoformat(),
                'metrics': {
                    'map50': float(val_results.box.map50) if hasattr(val_results, 'box') else None,
                    'map50_95': float(val_results.box.map) if hasattr(val_results, 'box') else None
                }
            }

            with open(eval_path, 'w') as f:
                json.dump(eval_metrics, f, indent=2)

        except Exception as e:
            logger.error(f"Evaluation failed: {e}")

    def export_model(self, format='onnx'):
        """Export trained model to different formats"""
        logger.info(f"Exporting model to {format} format...")

        # Find the best model weights
        weights_path = self.run_dir / 'weights' / 'best.pt'
        if not weights_path.exists():
            # Look for weights in the latest run directory
            run_dirs = list(self.runs_dir.glob('furniture_yolo11s_*'))
            if run_dirs:
                latest_run = max(run_dirs, key=lambda x: x.stat().st_mtime)
                weights_path = latest_run / 'weights' / 'best.pt'

        if weights_path.exists():
            model = YOLO(str(weights_path))
            export_path = model.export(format=format)
            logger.info(f"Model exported to {export_path}")
            return export_path
        else:
            logger.error("No trained model weights found!")
            return None

def main():
    """Main training function"""
    # Tự động kiểm tra và chọn thiết bị (GPU nếu có, không thì CPU)
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    if device == 'cpu':
        print("Cảnh báo: Huấn luyện trên CPU sẽ rất chậm. Nên sử dụng GPU nếu có thể.")

    print("Bắt đầu quá trình huấn luyện nâng cao...")

    try:
        # Initialize trainer
        trainer = FurnitureModelTrainer()

        # Train model
        results = trainer.train_model()

        # Export model
        trainer.export_model('onnx')
        trainer.export_model('torchscript')

        print("Quá trình huấn luyện hoàn tất thành công!")
        print(f"Model tốt nhất đã được lưu tại: {trainer.run_dir}")
        print("="*50)

    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"Lỗi trong quá trình huấn luyện: {e}")

if __name__ == '__main__':
    main()