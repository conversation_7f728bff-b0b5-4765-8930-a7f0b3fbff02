from ultralytics import YOLO
import torch

def main():
    # Tự động kiểm tra và chọn thiết bị (GPU nếu có, không thì CPU)
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    if device == 'cpu':
        print("Cảnh báo: Huấn luyện trên CPU sẽ rất chậm. Nên sử dụng GPU nếu có thể.")

    
    model = YOLO('yolov8n.pt')
    print("Bắt đầu quá trình huấn luyện...")
    
    result = model.train(data='furniture_data.yaml', epochs=50, imgsz=640, batch=50, device=device, name='furniture_model_final')
    
    print("Quá trình huấn luyện hoàn tất.")
    print(f"Model tốt nhất đã được lưu tại thư mục: runs/detect/furniture_model_final/weights/best.pt")
    print("="*50)

if __name__ == '__main__':
    main()