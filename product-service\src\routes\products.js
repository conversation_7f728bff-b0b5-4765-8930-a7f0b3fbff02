const express = require('express');
const router = express.Router();
const { pool, redisClient } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const productSchema = Joi.object({
  name: Joi.string().required().max(200),
  description: Joi.string().allow(''),
  price: Joi.number().positive().required(),
  sale_price: Joi.number().positive().allow(null),
  category_id: Joi.number().integer().positive().required(),
  brand: Joi.string().max(100).allow(''),
  material: Joi.string().max(100).allow(''),
  dimensions: Joi.string().allow(''),
  weight: Joi.number().positive().allow(null),
  stock_quantity: Joi.number().integer().min(0).default(0),
  min_stock_level: Joi.number().integer().min(0).default(5)
});

const updateProductSchema = Joi.object({
  name: Joi.string().max(200),
  description: Joi.string().allow(''),
  price: Joi.number().positive(),
  sale_price: Joi.number().positive().allow(null),
  category_id: Joi.number().integer().positive(),
  brand: Joi.string().max(100).allow(''),
  material: Joi.string().max(100).allow(''),
  dimensions: Joi.string().allow(''),
  weight: Joi.number().positive().allow(null),
  stock_quantity: Joi.number().integer().min(0),
  min_stock_level: Joi.number().integer().min(0),
  is_active: Joi.boolean()
});

// Helper to clear product list caches
const clearProductListCache = async () => {
  const keys = await redisClient.keys('products:list:*');
  if (keys.length > 0) {
    await redisClient.del(keys);
  }
};

// GET /api/products - Lấy danh sách sản phẩm
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category_id, 
      brand, 
      min_price, 
      max_price,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    // Create a unique cache key based on query params
    const cacheKey = `products:list:${JSON.stringify(req.query)}`;
    
    // Check cache first
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    const offset = (page - 1) * limit;
    
    // Build query
    let query = `
      SELECT p.*, c.name as category_name,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = true
    `;
    
    const queryParams = [];
    let paramIndex = 1;

    if (category_id) {
      query += ` AND p.category_id IN (
        WITH RECURSIVE subcategories AS (
          SELECT id FROM categories WHERE id = $${paramIndex}
          UNION ALL
          SELECT c.id FROM categories c
          INNER JOIN subcategories s ON s.id = c.parent_id
        )
        SELECT id FROM subcategories
      )`;
      queryParams.push(category_id);
      paramIndex++;
    }

    if (brand) {
      query += ` AND p.brand ILIKE $${paramIndex}`;
      queryParams.push(`%${brand}%`);
      paramIndex++;
    }

    if (min_price) {
      query += ` AND p.price >= $${paramIndex}`;
      queryParams.push(min_price);
      paramIndex++;
    }

    if (max_price) {
      query += ` AND p.price <= $${paramIndex}`;
      queryParams.push(max_price);
      paramIndex++;
    }

    // Add sorting
    const allowedSortFields = ['name', 'price', 'created_at', 'stock_quantity'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY p.${sortField} ${sortDirection}`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.is_active = true
    `;
    
    const countParams = [];
    let countParamIndex = 1;

    if (category_id) {
      countQuery += ` AND p.category_id IN (
        WITH RECURSIVE subcategories AS (
          SELECT id FROM categories WHERE id = $${countParamIndex}
          UNION ALL
          SELECT c.id FROM categories c
          INNER JOIN subcategories s ON s.id = c.parent_id
        )
        SELECT id FROM subcategories
      )`;
      countParams.push(category_id);
      countParamIndex++;
    }

    if (brand) {
      countQuery += ` AND p.brand ILIKE $${countParamIndex}`;
      countParams.push(`%${brand}%`);
      countParamIndex++;
    }

    if (min_price) {
      countQuery += ` AND p.price >= $${countParamIndex}`;
      countParams.push(min_price);
      countParamIndex++;
    }

    if (max_price) {
      countQuery += ` AND p.price <= $${countParamIndex}`;
      countParams.push(max_price);
      countParamIndex++;
    }

    const [productsResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const products = productsResult.rows.map(p => ({
      ...p,
      price: p.price !== null ? Number(p.price) : p.price,
      sale_price: p.sale_price !== null ? Number(p.sale_price) : p.sale_price
    }));
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const responseData = {
      products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    // Cache the result for 5 minutes (300 seconds)
    await redisClient.setEx(cacheKey, 300, JSON.stringify(responseData));

    res.json(responseData);

  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/products/:id - Lấy chi tiết sản phẩm
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check cache first
    const cacheKey = `product:${id}`;
    const cachedProduct = await redisClient.get(cacheKey);
    
    if (cachedProduct) {
      return res.json(JSON.parse(cachedProduct));
    }

    // Get product details
    const productQuery = `
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = $1 AND p.is_active = true
    `;

    const imagesQuery = `
      SELECT * FROM product_images 
      WHERE product_id = $1 
      ORDER BY is_primary DESC, sort_order ASC
    `;

    const [productResult, imagesResult] = await Promise.all([
      pool.query(productQuery, [id]),
      pool.query(imagesQuery, [id])
    ]);

    if (productResult.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const raw = productResult.rows[0];
    const product = {
      ...raw,
      price: raw.price !== null ? Number(raw.price) : raw.price,
      sale_price: raw.sale_price !== null ? Number(raw.sale_price) : raw.sale_price,
      images: imagesResult.rows
    };

    // Cache for 1 hour
    await redisClient.setEx(cacheKey, 3600, JSON.stringify(product));

    res.json(product);

  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/products - Tạo sản phẩm mới
router.post('/', async (req, res) => {
  try {
    const { error, value } = productSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const {
      name, description, price, sale_price, category_id,
      brand, material, dimensions, weight, stock_quantity, min_stock_level
    } = value;

    const query = `
      INSERT INTO products (
        name, description, price, sale_price, category_id,
        brand, material, dimensions, weight, stock_quantity, min_stock_level
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const result = await pool.query(query, [
      name, description, price, sale_price, category_id,
      brand, material, dimensions, weight, stock_quantity, min_stock_level
    ]);

    // Invalidate product list cache
    await clearProductListCache();

    res.status(201).json(result.rows[0]);

  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/products/:id - Cập nhật sản phẩm
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { error, value } = updateProductSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    Object.keys(value).forEach(key => {
      updateFields.push(`${key} = $${paramIndex}`);
      updateValues.push(value[key]);
      paramIndex++;
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(id);

    const query = `
      UPDATE products 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND is_active = true
      RETURNING *
    `;

    const result = await pool.query(query, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Clear cache
    await redisClient.del(`product:${id}`);
    await clearProductListCache();

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/products/:id - Xóa sản phẩm (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      UPDATE products 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING id
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Clear cache
    await redisClient.del(`product:${id}`);
    await clearProductListCache();

    res.json({ message: 'Product deleted successfully' });

  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;