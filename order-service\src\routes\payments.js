const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const processPaymentSchema = Joi.object({
  order_id: Joi.number().integer().positive().required(),
  payment_method: Joi.string().valid('credit_card', 'paypal', 'bank_transfer', 'cash_on_delivery').required(),
  payment_details: Joi.object().optional()
});

const updatePaymentStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'processing', 'completed', 'failed', 'refunded').required(),
  transaction_id: Joi.string().allow(''),
  notes: Joi.string().allow('')
});

// Helper function to generate transaction ID
function generateTransactionId() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `TXN${timestamp.slice(-8)}${random}`;
}

// GET /api/payments - Lấy danh sách thanh toán
router.get('/', authenticateToken, authorizeRoles('admin', 'staff'), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status,
      payment_method,
      user_id,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    let query = `
      SELECT p.*, o.order_number, o.total_amount as order_amount,
             u.first_name, u.last_name, u.email
      FROM payments p
      JOIN orders o ON p.order_id = o.id
      JOIN users u ON o.user_id = u.id
      WHERE 1=1
    `;
    
    const queryParams = [];
    let paramIndex = 1;

    if (status) {
      query += ` AND p.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    if (payment_method) {
      query += ` AND p.payment_method = $${paramIndex}`;
      queryParams.push(payment_method);
      paramIndex++;
    }

    if (user_id) {
      query += ` AND o.user_id = $${paramIndex}`;
      queryParams.push(user_id);
      paramIndex++;
    }

    // Add sorting
    const allowedSortFields = ['created_at', 'amount', 'status'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY p.${sortField} ${sortDirection}`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM payments p
      JOIN orders o ON p.order_id = o.id
      WHERE 1=1
    `;
    
    const countParams = [];
    let countParamIndex = 1;

    if (status) {
      countQuery += ` AND p.status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    if (payment_method) {
      countQuery += ` AND p.payment_method = $${countParamIndex}`;
      countParams.push(payment_method);
      countParamIndex++;
    }

    if (user_id) {
      countQuery += ` AND o.user_id = $${countParamIndex}`;
      countParams.push(user_id);
      countParamIndex++;
    }

    const [paymentsResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const payments = paymentsResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      payments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/payments/:id - Lấy chi tiết thanh toán
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;

    let query = `
      SELECT p.*, o.order_number, o.total_amount as order_amount, o.user_id,
             u.first_name, u.last_name, u.email
      FROM payments p
      JOIN orders o ON p.order_id = o.id
      JOIN users u ON o.user_id = u.id
      WHERE p.id = $1
    `;

    const queryParams = [id];

    // Regular users can only see payments for their own orders
    if (userRole === 'customer') {
      query += ` AND o.user_id = $2`;
      queryParams.push(userId);
    }

    const result = await pool.query(query, queryParams);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Error fetching payment:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/payments - Xử lý thanh toán
router.post('/', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const userId = req.user.userId;
    const { error, value } = processPaymentSchema.validate(req.body);
    
    if (error) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: error.details[0].message });
    }

    const { order_id, payment_method, payment_details } = value;

    // Check if order exists and belongs to user (for regular users)
    let orderQuery = `
      SELECT id, user_id, total_amount, status, payment_status
      FROM orders
      WHERE id = $1
    `;

    const orderQueryParams = [order_id];

    if (req.user.role === 'customer') {
      orderQuery += ` AND user_id = $2`;
      orderQueryParams.push(userId);
    }

    const orderResult = await client.query(orderQuery, orderQueryParams);

    if (orderResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Check if order can be paid
    if (order.status === 'cancelled') {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Cannot pay for cancelled order' });
    }

    if (order.payment_status === 'paid') {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Order is already paid' });
    }

    // Check if payment already exists for this order
    const existingPaymentResult = await client.query(
      'SELECT id FROM payments WHERE order_id = $1',
      [order_id]
    );

    if (existingPaymentResult.rows.length > 0) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Payment already exists for this order' });
    }

    // Generate transaction ID
    const transactionId = generateTransactionId();

    // Create payment record
    const paymentQuery = `
      INSERT INTO payments (order_id, amount, payment_method, status, transaction_id, payment_details)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    // Determine initial status based on payment method
    let initialStatus = 'pending';
    if (payment_method === 'cash_on_delivery') {
      initialStatus = 'pending'; // Will be completed on delivery
    } else {
      initialStatus = 'processing'; // For online payments
    }

    const paymentResult = await client.query(paymentQuery, [
      order_id,
      order.total_amount,
      payment_method,
      initialStatus,
      transactionId,
      JSON.stringify(payment_details || {})
    ]);

    const payment = paymentResult.rows[0];

    // Update order payment status
    await client.query(
      'UPDATE orders SET payment_status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [initialStatus === 'processing' ? 'processing' : 'pending', order_id]
    );

    await client.query('COMMIT');

    res.status(201).json({
      message: 'Payment initiated successfully',
      payment: payment
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error processing payment:', error);
    res.status(500).json({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

// PUT /api/payments/:id/status - Cập nhật trạng thái thanh toán (Admin/Staff only)
router.put('/:id/status', authenticateToken, authorizeRoles('admin', 'staff'), async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { error, value } = updatePaymentStatusSchema.validate(req.body);
    
    if (error) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: error.details[0].message });
    }

    const { status, transaction_id, notes } = value;

    // Get payment and order details
    const paymentQuery = `
      SELECT p.*, o.id as order_id, o.payment_status as order_payment_status
      FROM payments p
      JOIN orders o ON p.order_id = o.id
      WHERE p.id = $1
    `;

    const paymentResult = await client.query(paymentQuery, [id]);

    if (paymentResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Payment not found' });
    }

    const payment = paymentResult.rows[0];

    // Update payment status
    const updateQuery = `
      UPDATE payments 
      SET status = $1, 
          transaction_id = COALESCE($2, transaction_id),
          notes = COALESCE($3, notes),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `;

    const updateResult = await client.query(updateQuery, [status, transaction_id, notes, id]);

    // Update order payment status based on payment status
    let orderPaymentStatus = payment.order_payment_status;
    
    if (status === 'completed') {
      orderPaymentStatus = 'paid';
    } else if (status === 'failed') {
      orderPaymentStatus = 'failed';
    } else if (status === 'refunded') {
      orderPaymentStatus = 'refunded';
    }

    await client.query(
      'UPDATE orders SET payment_status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [orderPaymentStatus, payment.order_id]
    );

    await client.query('COMMIT');

    res.json({
      message: 'Payment status updated successfully',
      payment: updateResult.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating payment status:', error);
    res.status(500).json({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

// GET /api/payments/stats/overview - Thống kê thanh toán (Admin only)
router.get('/stats/overview', authenticateToken, authorizeRoles('admin'), async (req, res) => {
  try {
    const statsQuery = `
      SELECT 
        COUNT(*) as total_payments,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_payments,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_payments,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
        COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0) as total_revenue,
        COALESCE(AVG(amount) FILTER (WHERE status = 'completed'), 0) as avg_payment_amount,
        COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as payments_last_30_days,
        COALESCE(SUM(amount) FILTER (WHERE status = 'completed' AND created_at >= NOW() - INTERVAL '30 days'), 0) as revenue_last_30_days
      FROM payments
    `;

    const result = await pool.query(statsQuery);
    const stats = result.rows[0];

    // Convert string numbers to appropriate types
    Object.keys(stats).forEach(key => {
      if (key.includes('total') || key.includes('count') || key.includes('payments')) {
        stats[key] = parseInt(stats[key]);
      } else {
        stats[key] = parseFloat(stats[key]);
      }
    });

    res.json({ stats });

  } catch (error) {
    console.error('Error getting payment stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
