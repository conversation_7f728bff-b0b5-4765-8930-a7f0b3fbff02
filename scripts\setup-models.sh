#!/bin/bash

# Setup AI Models for Furniture Detection
echo "🤖 Setting up AI models for furniture detection..."

# Create models directory
mkdir -p ai-service/models

# Download YOLO models
echo "📥 Downloading YOLO models..."

# YOLOv8 Nano (fastest, good for testing)
if [ ! -f "ai-service/models/yolov8n.pt" ]; then
    echo "Downloading YOLOv8 Nano..."
    curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt" -o ai-service/models/yolov8n.pt
else
    echo "✅ YOLOv8 Nano already exists"
fi

# YOLOv8 Small (better accuracy)
if [ ! -f "ai-service/models/yolov8s.pt" ]; then
    echo "Downloading YOLOv8 Small..."
    curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt" -o ai-service/models/yolov8s.pt
else
    echo "✅ YOLOv8 Small already exists"
fi

# YOLOv8 Medium (best balance)
if [ ! -f "ai-service/models/yolov8m.pt" ]; then
    echo "Downloading YOLOv8 Medium..."
    curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt" -o ai-service/models/yolov8m.pt
else
    echo "✅ YOLOv8 Medium already exists"
fi

# YOLOv11 Segmentation (for advanced detection)
if [ ! -f "ai-service/models/yolo11s-seg.pt" ]; then
    echo "Downloading YOLOv11 Segmentation..."
    curl -L "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-seg.pt" -o ai-service/models/yolo11s-seg.pt
else
    echo "✅ YOLOv11 Segmentation already exists"
fi

# Create sample images directory
mkdir -p ai-service/sample-images

# Download sample furniture images for testing
echo "📸 Downloading sample furniture images..."

sample_images=(
    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80"
    "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&q=80"
    "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80"
)

for i in "${!sample_images[@]}"; do
    if [ ! -f "ai-service/sample-images/sample_$((i+1)).jpg" ]; then
        echo "Downloading sample image $((i+1))..."
        curl -L "${sample_images[$i]}" -o "ai-service/sample-images/sample_$((i+1)).jpg"
    fi
done

echo ""
echo "✅ Model setup complete!"
echo ""
echo "📊 Downloaded models:"
ls -la ai-service/models/
echo ""
echo "🎯 Model recommendations:"
echo "   • yolov8n.pt  - Fastest (development/testing)"
echo "   • yolov8s.pt  - Good balance (recommended)"
echo "   • yolov8m.pt  - Better accuracy (production)"
echo "   • yolo11s-seg.pt - Advanced segmentation"
echo ""
echo "🔧 To change model, update YOLO_MODEL_PATH in .env file"
