# subsample_dataset.py
import os
import random
import shutil
from tqdm import tqdm

# --- CẤU HÌNH ---
# Th<PERSON> mục nguồn chứa bộ dữ liệu đầy đủ
SOURCE_DIR = "final_dataset"

# Thư mục đích để chứa bộ dữ liệu con
DEST_DIR = "mini_dataset"

# Tỷ lệ phần trăm dữ liệu bạn muốn lấy (ví dụ: 0.25 = 25%)
SAMPLE_FRACTION = 0.25

def subsample_split(split='train'):
    """Lấy mẫu một phần của một tập dữ liệu (train/val)."""
    print(f"\nBắt đầu lấy mẫu cho tập '{split}'...")
    
    source_images = os.path.join(SOURCE_DIR, "images", split)
    source_labels = os.path.join(SOURCE_DIR, "labels", split)
    
    dest_images = os.path.join(DEST_DIR, "images", split)
    dest_labels = os.path.join(DEST_DIR, "labels", split)
    
    os.makedirs(dest_images, exist_ok=True)
    os.makedirs(dest_labels, exist_ok=True)
    
    if not os.path.exists(source_images):
        print(f"Thư mục nguồn không tồn tại: {source_images}")
        return

    all_images = [f for f in os.listdir(source_images) if f.endswith(('.jpg', '.png'))]
    random.shuffle(all_images)
    
    num_to_sample = int(len(all_images) * SAMPLE_FRACTION)
    sampled_images = all_images[:num_to_sample]
    
    print(f"Tổng số ảnh gốc: {len(all_images)}. Sẽ lấy mẫu: {len(sampled_images)} ảnh.")
    
    for img_filename in tqdm(sampled_images, desc=f"Sao chép tập '{split}'"):
        label_filename = os.path.splitext(img_filename)[0] + '.txt'
        
        # Sao chép file ảnh
        shutil.copy(os.path.join(source_images, img_filename), os.path.join(dest_images, img_filename))
        
        # Sao chép file label tương ứng
        source_label_path = os.path.join(source_labels, label_filename)
        if os.path.exists(source_label_path):
            shutil.copy(source_label_path, os.path.join(dest_labels, label_filename))

def main():
    if os.path.exists(DEST_DIR):
        print(f"Thư mục '{DEST_DIR}' đã tồn tại. Vui lòng xóa hoặc đổi tên trước khi chạy.")
        return
        
    print(f"Bắt đầu tạo bộ dữ liệu con ({SAMPLE_FRACTION * 100}%) tại '{DEST_DIR}'...")
    subsample_split('train')
    subsample_split('val')
    print("\n✅ Hoàn tất! Bộ dữ liệu con đã sẵn sàng.")

if __name__ == "__main__":
    main()