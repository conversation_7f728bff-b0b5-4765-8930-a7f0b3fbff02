import json
import os
import shutil
from tqdm import tqdm
from PIL import Image

# ==============================================================================
# PHẦN CẤU HÌNH - BẠN CẦN CHỈNH SỬA CÁC ĐƯỜNG DẪN NÀY
# ==============================================================================

# Đường dẫn đến file info.json của bạn
input_json_path = 'raw_data/sunrgbd/archive/MYSUN/info.json'

image_base_path = "raw_data/sunrgbd/archive/MYSUN/image" # Đường dẫn đến thư mục chứa ảnh

output_dir = "processed_data"  # Đường dẫn đến thư mục đầu ra

CLASS_MAPPING = {
    'air_conditioner': ['air_condition', 'air_conditioner', 'airconditioner'],
    'backpack': ['back_pack', 'bagpack'],
    'bag': ['bag', 'bags', 'bean_bag', 'carry_bag', 'carrybag', 'gym_bag', 'hand_bag', 'handbag', 'messenger_bag', 'messengerbag', 'paper_bag', 'paperbag', 'plastic_bag', 'pouch_bag', 'punching_bag', 'sling_bag', 'tennis_bag', 'travelingbag', 'weekender'],
    'basket': ['basket', 'flower_basket', 'laundry_basket', 'laundrybasket', 'plastic_basket'],
    'bed': ['baby_bed', 'bed', 'bunk_bed', 'child_bed', 'folding_bed', 'futon', 'sofa_bed'],
    'bench': ['bench', 'piano_bench'],
    'bicycle': ['bicycle', 'cycle'],
    'bin': ['bin', 'garbage_bin', 'garbage_bin ', 'plastic_bin', 'recycle_bin'],
    'blanket': ['blanket'],
    'blinds': ['blinds', 'curtain_blinds', 'venetian_blinds', 'venetianblinds', 'vishal_blinds'],
    'book': ['book', 'booklet', 'books', 'clear_book', 'notebook', 'pocketbook', 'songbook'],
    'bookshelf': ['book_organizer', 'bookcase', 'bookshelf', 'bookstand', 'magazine_rack', 'toy_rack'],
    'bottle': ['bottle', 'bottle_spray', 'bottled_water', 'cleaner_bottle', 'liquid_bottle', 'medicine_bottle', 'mineral_bottle', 'shampoo_bottle', 'spray_bottle', 'water_bottle', 'wine_bottle'],
    'bowl': ['bowl', 'fruitbowl', 'mixing_bowl', 'urinal_bowl', 'washbowl'],
    'box': ['assorted_boxes', 'box', 'box_of_clothes', 'boxes', 'chair_box', 'chocolate_box', 'dvd_pack', 'file_box', 'file_box_with_papers', 'flower_box', 'fuse_box', 'hose_box', 'ice_box', 'letter_box', 'light_box', 'lunch_box', 'lunchbox', 'magazine_boxes', 'meter_box', 'music_stand', 'pen_stand', 'pile_of_boxes', 'pipe_joint', 'pizza_box', 'plant_box', 'shoe_box', 'sound_box', 'storage_box', 'tin_box', 'tissuebox', 'tool_box', 'toolbox'],
    'cabinet': ['cabinet', 'card_catalog', 'display_cabinet', 'file_cabinet', 'kitchen_wardrobe', 'medicine_kit'],
    'camera': ['camera', 'cctv_camera', 'webcam'],
    'carpet': ['carpet', 'rug'],
    'chair': ['baby_chair', 'chair', 'chair_with_desk', 'chairs', 'child_chair', 'high_chair', 'lawn_chair', 'lounge_chair', 'massage_chair', 'recliner', 'rocking_chair', 'saucer_chair', 'sofa_chair', 'stack_of_chairs'],
    'clock': ['alarm', 'clock', 'grandfatherclock', 'hand_clock', 'wallclock'],
    'coat_rack': ['coat_rack', 'coat_stand', 'hat_and_coat_rack'],
    'computer': ['computer', 'comupter', 'laptop'],
    'couch': ['couch', 'sofa'],
    'cup': ['coffee_cup', 'cup', 'cup_and_saucer', 'cup_with_pens', 'cup_with_toothbrush', 'cups', 'paper_cup'],
    'curtain': ['curtain', 'shower_curtain', 'showercurtain'],
    'cushion': ['cushion', 'pillow'],
    'desk': ['desk'],
    'door': ['cdoor', 'dooe', 'door', 'hingedoor', 'lift_door'],
    'drawer': ['brawer', 'dawer', 'drawer', 'drawers', 'file_drawer', 'mini_drawers'],
    'dresser': ['armoire', 'chest', 'credenza', 'dresser', 'kitchen_wardrobe', 'wardrobe'],
    'fan': ['airfan', 'ceiling_fan', 'electric_fan', 'fan', 'laptop_cooling_fan', 'tablefan'],
    'faucet': ['faucet', 'tap'],
    'fireplace': ['fire_place', 'fireplace'],
    'flower': ['flower', 'flowers'],
    'keyboard': ['computer_keyboard', 'computer_monitor_and_keyboard', 'computer_monitor_with_keyboard', 'key_board', 'keyboard', 'keyboard_mouse'],
    'kitchen_sink': ['kitchen_sink', 'sink'],
    'knife': ['knife', 'knives'],
    'lamp': ['chandelier', 'lamp', 'street_lamp', 'walllamp'],
    'machine': ['addingmachine', 'atm_machine', 'coffee_machine', 'drilling_machine', 'espresso_machine', 'fax_machine', 'kiosk_machine', 'lamination_machine', 'laminator_machine', 'machine', 'pinball_machine', 'sealer', 'tellermachine', 'token_machine', 'typewriter', 'vending_machine', 'washing_machine'],
    'microwave': ['microwave', 'microwave_oven'],
    'mirror': ['dresser_mirror', 'mirror'],
    'monitor': ['computer_monitor_and_keyboard', 'computer_monitor_with_keyboard', 'flat_screen_tv', 'monitor', 'screen', 'television', 'tv'],
    'mouse': ['computer_mouse', 'keyboard_mouse', 'mouse', 'mouse_&_mouse_pad', 'mouse_mouse_pad'],
    'oven': ['microwave_oven', 'oven', 'oven_toaster', 'toaster_oven', 'toasteroven'],
    'person': ['child', 'man', 'mannequin', 'person'],
    'picture': ['art', 'frames', 'painting', 'photo', 'picture', 'pictureframe', 'pictures', 'portrait', 'poster', 'posters', 'wall_painting'],
    'plant': ['artificial_plant', 'cactus', 'flower', 'flowers', 'leaves', 'plant', 'plant_in_pot', 'plants', 'tree', 'wreath'],
    'plate': ['dogplate', 'food_plate', 'nameplate', 'paper_plate', 'plate', 'plate_set', 'set_of_plates'],
    'pot': ['airpot', 'brush_pot', 'coffee_pot', 'cooking_pot', 'flower_pot', 'plant_pot', 'pot', 'rice_pot', 'tea_pot', 'water_pot'],
    'potted_plant': ['artificial_plant', 'plant', 'plant_in_pot', 'plants'],
    'printer': ['poster_printer', 'printer'],
    'rack': ['cd_case', 'clothes_rack', 'clothing_rack', 'computer_rack', 'computerrack', 'display_rack', 'drying_rack', 'dryingrack', 'fruit_rack', 'hat_and_coat_rack', 'laundry_rack', 'magazine_rack', 'plate_rack', 'rack', 'shoe_rack', 'spicerack', 'test_tube_rack', 'towel_holder', 'tube_rack', 'water_rack'],
    'refrigerator': ['freezer', 'fridge', 'frige', 'mini_bar', 'mini_refrigerator'],
    'remote_control': ['remote', 'remote_conrol', 'remote_control', 'remotecontrol'],
    'shelf': ['bookshelf', 'mini_shelf', 'shelf', 'shelr', 'shelves'],
    'shoe': ['boots', 'keds', 'rubber_shoes', 'sandal', 'shoe', 'shoes', 'slipper', 'slippers'],
    'shower': ['shower', 'showerroom'],
    'speaker': ['loudspeaker', 'soeaker', 'sound_system', 'soundsystem', 'speaker', 'tower_speakers'],
    'sports_ball': ['ball', 'basketballhoop', 'billiard_balls', 'exercise_ball'],
    'stool': ['bar_table', 'footrest', 'luggage_stool', 'piano_stool', 'stack_of_stools', 'stepstool', 'stool'],
    'stop_sign': ['caution_sign', 'cautionsign', 'exit_sign', 'floor_sign', 'sigh', 'sign'],
    'stove': ['burner', 'cook-top', 'electricstove', 'gas_stove', 'gasrange', 'portable_stove', 'stove'],
    'suitcase': ['attachecase', 'briefcase', 'luggage', 'suits_case'],
    'table': ['bar_table', 'billiard_table', 'buffettable', 'centertable', 'coffee_table', 'coffeetable', 'console_table', 'desk', 'dining_table', 'display_stand', 'end_table', 'endtable', 'entable', 'foosball_table', 'island', 'laboratory_table', 'long_office_table', 'machine_stand', 'night_stand', 'nightstand', 'pedestal', 'ping_pong_table', 'pingpongtable', 'pool_table', 'side_table', 'sidetable', 'soccer_table', 'stand', 'table', 'table_tennis', 'tv_stand', 'vanity_table'],
    'telephone': ['cell_phone', 'cellphone', 'cordless_phone', 'iphone', 'mobile', 'mobile_phone', 'phone', 'smartphone', 'telephone'],
    'toilet': ['comode', 'toilet', 'urinal'],
    'towel': ['bathtowel', 'paper_towel', 'papertowel', 'papertowels', 'towel'],
    'toy': ['doll', 'lego', 'robot', 'stacks_of_large_lego_blocks', 'stuffed_toy', 'teddybear', 'toy', 'toy_car', 'toy_house', 'toy_plane', 'toy_train', 'toyhorse', 'toyhouse', 'toys', 'trampoline'],
    'traffic_light': ['emergencylight', 'light'],
    'trash_can': ['ashcan', 'dustbin', 'garbage_bin', 'garbage_bin ', 'hamper', 'waste_basket'],
    'umbrella': ['umbrella'],
    'utensil': ['dining_utensils', 'fork', 'kitchenutensils', 'ladle', 'spoon', 'utensil', 'utensils'],
    'vase': ['flower_base', 'flower_vase', 'flowerbase', 'jar', 'jars', 'vase'],
    'washing_machine': ['drier', 'dryer', 'dryers', 'washer', 'washing_machine'],
    'window': ['glass_window', 'window'],
}

# ==============================================================================
# PHẦN LOGIC XỬ LÝ - BẠN KHÔNG CẦN CHỈNH SỬA BÊN DƯỚI
# ==============================================================================

def convert_annotations():
    print("Bắt đầu quá trình chuyển đổi dữ liệu SUNRGBD...")
    
    unified_class_names = list(CLASS_MAPPING.keys())
    
    output_images_dir = os.path.join(output_dir, "images", "train")
    output_labels_dir = os.path.join(output_dir, "labels", "train")
    
    os.makedirs(output_images_dir, exist_ok=True)
    os.makedirs(output_labels_dir, exist_ok=True)
    
    with open(input_json_path, 'r') as f:
        data = json.load(f)
        
        # Lặp qua từng ảnh trong file JSON
    for item in tqdm(data, desc="Processing images"):
        img_path = os.path.join(image_base_path, os.path.basename(item['image']))
        img_filename = os.path.basename(img_path)

        # Dùng thư viện Pillow để mở ảnh và lấy kích thước
        try:
            with Image.open(img_path) as img:
                img_width, img_height = img.size
        except FileNotFoundError:
            print(f"!!! Cảnh báo: Không tìm thấy file ảnh: {img_path}. Bỏ qua...")
            continue # Bỏ qua ảnh này và tiếp tục vòng lặp

        yolo_annotations = []

        # Lặp qua từng annotation của ảnh
        for ann in item.get('annotations', []):
            original_label = ann['classname']
            
            # Tìm xem label gốc thuộc lớp thống nhất nào
            unified_name = None
            for key, values in CLASS_MAPPING.items():
                if original_label in values:
                    unified_name = key
                    break
            
            # Bỏ qua nếu lớp này không nằm trong bản đồ
            if unified_name is None:
                continue

            # Lấy class ID thống nhất
            class_id = unified_class_names.index(unified_name)
            
            # Lấy bounding box - Giả định định dạng là [x_min, y_min, width, height]
            bbox = ann['bbox']
            x_min, y_min, w, h = bbox[0], bbox[1], bbox[2], bbox[3]

            # Chuyển đổi sang định dạng YOLO
            x_center = (x_min + w / 2) / img_width
            y_center = (y_min + h / 2) / img_height
            width_norm = w / img_width
            height_norm = h / img_height

            yolo_line = f"{class_id} {x_center:.6f} {y_center:.6f} {width_norm:.6f} {height_norm:.6f}"
            yolo_annotations.append(yolo_line)

        # Nếu có annotation hợp lệ, ghi ra file .txt và copy ảnh
        if yolo_annotations:
            # Ghi file label
            label_filename = os.path.splitext(img_filename)[0] + '.txt'
            with open(os.path.join(output_labels_dir, label_filename), 'w') as f:
                f.write("\n".join(yolo_annotations))

            # Copy file ảnh
            shutil.copy(os.path.join(image_base_path, img_filename), output_images_dir)

    print("\nQuá trình chuyển đổi hoàn tất!")
    print(f"Dữ liệu đã được xử lý và lưu tại: {output_dir}")
    
if __name__ == "__main__":
    convert_annotations()