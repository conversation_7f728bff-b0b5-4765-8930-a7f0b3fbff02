const jwt = require('jsonwebtoken');
const { redisClient } = require('../config/database');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';

class JWTUtils {
  static generateTokens(payload) {
    const accessToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN
    });

    const refreshToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: REFRESH_TOKEN_EXPIRES_IN
    });

    return { accessToken, refreshToken };
  }

  static verifyToken(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  static async blacklistToken(token) {
    try {
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.setEx(`blacklist:${token}`, ttl, 'true');
        }
      }
    } catch (error) {
      console.error('Error blacklisting token:', error);
    }
  }

  static async isTokenBlacklisted(token) {
    try {
      const result = await redisClient.get(`blacklist:${token}`);
      return result === 'true';
    } catch (error) {
      console.error('Error checking blacklisted token:', error);
      return false;
    }
  }

  static async storeRefreshToken(userId, refreshToken) {
    try {
      const decoded = jwt.decode(refreshToken);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.setEx(`refresh:${userId}`, ttl, refreshToken);
        }
      }
    } catch (error) {
      console.error('Error storing refresh token:', error);
    }
  }

  static async getRefreshToken(userId) {
    try {
      return await redisClient.get(`refresh:${userId}`);
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  }

  static async removeRefreshToken(userId) {
    try {
      await redisClient.del(`refresh:${userId}`);
    } catch (error) {
      console.error('Error removing refresh token:', error);
    }
  }
}

module.exports = JWTUtils;
