#!/bin/bash

# Integration Testing Script for Furniture Store
echo "🧪 Running Integration Tests for Furniture Store..."

API_BASE="http://localhost:8000"
FRONTEND_URL="http://localhost:3000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5

    echo -n "Testing: $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /dev/null "$API_BASE$endpoint")
    elif [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -o /dev/null -X POST -H "Content-Type: application/json" -d "$data" "$API_BASE$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -o /dev/null -X "$method" "$API_BASE$endpoint")
    fi

    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL (Expected: $expected_status, Got: $response)${NC}"
        ((TESTS_FAILED++))
    fi
}

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

echo ""
echo "🔍 Testing Service Health Checks..."

# Test health endpoints
test_endpoint "GET" "/health" "200" "API Gateway Health Check"
test_endpoint "GET" "/api/ai/health" "200" "AI Service Health Check"

echo ""
echo "🔐 Testing Authentication Endpoints..."

# Test auth endpoints (should return 400 for missing data, not 500)
test_endpoint "POST" "/api/auth/login" "400" "Login endpoint (no data)"
test_endpoint "POST" "/api/auth/register" "400" "Register endpoint (no data)"

echo ""
echo "🛍️ Testing Product Endpoints..."

# Test product endpoints
test_endpoint "GET" "/api/products" "200" "Get products list"
test_endpoint "GET" "/api/categories" "200" "Get categories list"
test_endpoint "GET" "/api/search?q=chair" "200" "Search products"

echo ""
echo "🛒 Testing Cart Endpoints (should require auth)..."

# Test cart endpoints (should return 401 for unauthorized)
test_endpoint "GET" "/api/cart" "401" "Get cart (unauthorized)"
test_endpoint "POST" "/api/cart/items" "401" "Add to cart (unauthorized)"

echo ""
echo "📦 Testing Order Endpoints (should require auth)..."

# Test order endpoints (should return 401 for unauthorized)
test_endpoint "GET" "/api/orders" "401" "Get orders (unauthorized)"
test_endpoint "POST" "/api/orders" "401" "Create order (unauthorized)"

echo ""
echo "🤖 Testing AI Detection Endpoints..."

# Test AI endpoints (should return 400 for missing file)
test_endpoint "POST" "/api/ai/detect" "400" "AI detection (no file)"

echo ""
echo "🌐 Testing Frontend Availability..."

# Test frontend
frontend_response=$(curl -s -w "%{http_code}" -o /dev/null "$FRONTEND_URL")
if [ "$frontend_response" = "200" ]; then
    echo -e "Frontend accessibility: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "Frontend accessibility: ${RED}❌ FAIL (Got: $frontend_response)${NC}"
    ((TESTS_FAILED++))
fi

echo ""
echo "📊 Testing Monitoring Endpoints..."

# Test monitoring
prometheus_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:9090")
if [ "$prometheus_response" = "200" ]; then
    echo -e "Prometheus accessibility: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "Prometheus accessibility: ${RED}❌ FAIL (Got: $prometheus_response)${NC}"
    ((TESTS_FAILED++))
fi

grafana_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:3001")
if [ "$grafana_response" = "200" ] || [ "$grafana_response" = "302" ]; then
    echo -e "Grafana accessibility: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "Grafana accessibility: ${RED}❌ FAIL (Got: $grafana_response)${NC}"
    ((TESTS_FAILED++))
fi

echo ""
echo "📋 Test Results Summary:"
echo "========================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! The system is working correctly.${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️  Some tests failed. Please check the services and try again.${NC}"
    echo ""
    echo "💡 Troubleshooting tips:"
    echo "   • Check if all services are running: docker-compose ps"
    echo "   • View service logs: docker-compose logs [service-name]"
    echo "   • Restart services: docker-compose restart"
    exit 1
fi
