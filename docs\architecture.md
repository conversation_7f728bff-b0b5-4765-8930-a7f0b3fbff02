# Kiến Trúc Hệ Thống - Trang Web Bán Nội Thất Tích Hợp AI

## Tổng Quan
Hệ thống được thiết kế theo kiến trúc microservices, cho phép mở rộng và bảo trì dễ dàng. Mỗi service có trách nhiệm riêng biệt và giao tiếp thông qua API Gateway.

## Kiến Trúc Tổng Thể

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser  │    │   Mobile App    │    │   Admin Panel   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API Gateway          │
                    │      (Port: 8000)        │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────▼────────┐    ┌──────────▼──────────┐    ┌────────▼────────┐
│  AI Service    │    │  Product Service    │    │  User Service   │
│ (Port: 8001)   │    │  (Port: 8002)      │    │ (Port: 8003)    │
└───────┬────────┘    └──────────┬──────────┘    └────────┬────────┘
        │                        │                        │
        └────────────────────────┼────────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Order Service         │
                    │     (Port: 8004)         │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────▼────────┐    ┌──────────▼──────────┐    ┌────────▼────────┐
│   PostgreSQL   │    │      Redis         │    │    RabbitMQ     │
│   Database     │    │     Cache          │    │  Message Queue  │
└────────────────┘    └────────────────────┘    └─────────────────┘
```

## Chi Tiết Các Service

### 1. AI Service (Port: 8001)
**Chức năng chính:**
- Nhận diện đồ nội thất từ ảnh sử dụng YOLO/RF-DETR
- Hỗ trợ kỹ thuật slicing để tăng độ chính xác
- Xử lý ảnh và trả về bounding box

**Công nghệ:**
- Python FastAPI
- Ultralytics YOLO
- OpenCV
- PyTorch

**API Endpoints:**
- `POST /detect` - Nhận diện thông thường
- `POST /detect-slice` - Nhận diện với slicing 2x2
- `POST /detect-super-slice` - Nhận diện với slicing 4x3
- `GET /models` - Liệt kê models có sẵn

### 2. Product Service (Port: 8002)
**Chức năng chính:**
- Quản lý danh mục sản phẩm
- Tìm kiếm sản phẩm tương tự
- Quản lý hình ảnh sản phẩm

**Công nghệ:**
- Node.js Express
- PostgreSQL
- Redis Cache

**API Endpoints:**
- `GET /api/products` - Lấy danh sách sản phẩm
- `GET /api/products/:id` - Lấy chi tiết sản phẩm
- `POST /api/products` - Tạo sản phẩm mới
- `GET /api/search` - Tìm kiếm sản phẩm

### 3. User Service (Port: 8003)
**Chức năng chính:**
- Quản lý người dùng
- Xác thực và phân quyền
- Quản lý profile

**Công nghệ:**
- Node.js Express
- JWT Authentication
- PostgreSQL
- Redis Cache

**API Endpoints:**
- `POST /api/users/register` - Đăng ký
- `POST /api/users/login` - Đăng nhập
- `GET /api/users/profile` - Lấy thông tin profile
- `PUT /api/users/profile` - Cập nhật profile

### 4. Order Service (Port: 8004)
**Chức năng chính:**
- Quản lý đơn hàng
- Xử lý thanh toán
- Quản lý giỏ hàng

**Công nghệ:**
- Node.js Express
- PostgreSQL
- RabbitMQ
- Redis Cache

**API Endpoints:**
- `POST /api/orders` - Tạo đơn hàng
- `GET /api/orders` - Lấy danh sách đơn hàng
- `PUT /api/orders/:id/status` - Cập nhật trạng thái
- `POST /api/cart` - Thêm vào giỏ hàng

### 5. API Gateway (Port: 8000)
**Chức năng chính:**
- Điều hướng request đến các service
- Rate limiting
- Load balancing
- Authentication middleware

**Công nghệ:**
- Node.js Express
- http-proxy-middleware
- Rate limiting

### 6. Web Frontend (Port: 3000)
**Chức năng chính:**
- Giao diện người dùng
- Upload ảnh và hiển thị kết quả
- Tìm kiếm và mua sản phẩm

**Công nghệ:**
- React.js
- TypeScript
- Tailwind CSS
- Axios

## Cơ Sở Dữ Liệu

### PostgreSQL
- **Database:** furniture_store
- **Tables:**
  - users
  - products
  - categories
  - orders
  - order_items
  - images

### Redis
- **Chức năng:** Cache session, product cache
- **TTL:** 1 giờ

### RabbitMQ
- **Chức năng:** Message queue cho xử lý đơn hàng
- **Queues:**
  - order_processing
  - email_notifications
  - inventory_updates

## Monitoring & Logging

### Prometheus
- Thu thập metrics từ tất cả services
- Scrape interval: 15s

### Grafana
- Dashboard hiển thị metrics
- Port: 3001

## Deployment

### Docker Compose
- Tất cả services được containerized
- Network: furniture_network
- Volumes: postgres_data, grafana_data

### Environment Variables
- Database connections
- Service URLs
- API keys
- JWT secrets

## Security

### Authentication
- JWT tokens
- Password hashing với bcrypt
- Rate limiting

### CORS
- Cho phép cross-origin requests
- Cấu hình linh hoạt

### Helmet
- Security headers
- XSS protection
- Content Security Policy

## Scalability

### Horizontal Scaling
- Mỗi service có thể scale độc lập
- Load balancer support
- Stateless design

### Caching Strategy
- Redis cache cho products
- In-memory caching
- CDN cho static assets

## Performance

### Optimization
- Database indexing
- Query optimization
- Connection pooling
- Async processing

### Monitoring
- Response time tracking
- Error rate monitoring
- Resource usage tracking
