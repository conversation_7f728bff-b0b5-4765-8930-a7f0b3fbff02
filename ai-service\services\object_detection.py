import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from pathlib import Path
import logging
from typing import List, Dict, Any
import asyncio

from models.detection_result import DetectionResult, BoundingBox, DetectedObject

logger = logging.getLogger(__name__)

class ObjectDetectionService:
    def __init__(self):
        self.model = None
        self.model_path = "models/yolo11s-seg.pt"
        self.load_model()
    
    def load_model(self):
        """Load YOLO model"""
        try:
            if Path(self.model_path).exists():
                self.model = YOLO(self.model_path)
                logger.info(f"Model loaded successfully: {self.model_path}")
            else:
                # Fallback to default YOLO model
                self.model = YOLO('yolo11s-seg.pt')
                logger.info("Using default YOLO model")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    async def detect_objects(self, image_path: str) -> DetectionResult:
        """
        <PERSON><PERSON>ậ<PERSON> <PERSON><PERSON><PERSON> vậ<PERSON> thể sử dụng YOLO thông thường
        """
        try:
            # Load ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            # Chạy inference
            results = self.model(image)
            
            # Xử lý kết quả
            detected_objects = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Lấy tọa độ bounding box
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0].cpu().numpy())
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = self.model.names[class_id]
                        
                        # Tạo bounding box
                        bbox = BoundingBox(
                            x=int(x1),
                            y=int(y1),
                            width=int(x2 - x1),
                            height=int(y2 - y1)
                        )
                        
                        # Tạo detected object
                        obj = DetectedObject(
                            class_name=class_name,
                            class_id=class_id,
                            confidence=confidence,
                            bounding_box=bbox
                        )
                        detected_objects.append(obj)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=detected_objects,
                total_objects=len(detected_objects)
            )
            
        except Exception as e:
            logger.error(f"Error in object detection: {e}")
            raise
    
    async def detect_objects_slice(self, image_path: str) -> DetectionResult:
        """
        Nhận diện vật thể sử dụng kỹ thuật slicing (từ YoloSlicing)
        Chia ảnh thành 4 lát (2x2) với overlap
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            height, width = image.shape[:2]
            
            # Chia ảnh thành 4 lát (2x2) với overlap 20%
            overlap = 0.2
            slice_width = int(width * (1 + overlap) / 2)
            slice_height = int(height * (1 + overlap) / 2)
            
            all_detections = []
            
            # Tạo 4 lát
            slices = [
                (0, 0, slice_width, slice_height),  # Top-left
                (int(width/2 - slice_width/2), 0, slice_width, slice_height),  # Top-right
                (0, int(height/2 - slice_height/2), slice_width, slice_height),  # Bottom-left
                (int(width/2 - slice_width/2), int(height/2 - slice_height/2), slice_width, slice_height)  # Bottom-right
            ]
            
            for i, (x, y, w, h) in enumerate(slices):
                # Cắt lát ảnh
                slice_img = image[y:y+h, x:x+w]
                
                # Chạy detection trên lát
                results = self.model(slice_img)
                
                # Xử lý kết quả và điều chỉnh tọa độ
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0].cpu().numpy())
                            class_id = int(box.cls[0].cpu().numpy())
                            class_name = self.model.names[class_id]
                            
                            # Điều chỉnh tọa độ về ảnh gốc
                            global_x1 = int(x1 + x)
                            global_y1 = int(y1 + y)
                            global_x2 = int(x2 + x)
                            global_y2 = int(y2 + y)
                            
                            # Tạo bounding box
                            bbox = BoundingBox(
                                x=global_x1,
                                y=global_y1,
                                width=global_x2 - global_x1,
                                height=global_y2 - global_y1
                            )
                            
                            # Tạo detected object
                            obj = DetectedObject(
                                class_name=class_name,
                                class_id=class_id,
                                confidence=confidence,
                                bounding_box=bbox
                            )
                            all_detections.append(obj)
            
            # Loại bỏ duplicate detections (NMS)
            filtered_detections = self._non_max_suppression(all_detections)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method="slicing_2x2"
            )
            
        except Exception as e:
            logger.error(f"Error in slice detection: {e}")
            raise
    
    async def detect_objects_super_slice(self, image_path: str) -> DetectionResult:
        """
        Nhận diện vật thể sử dụng kỹ thuật super slicing (4x3)
        Chia ảnh thành 12 lát với overlap lớn hơn
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            height, width = image.shape[:2]
            
            # Chia ảnh thành 12 lát (4x3) với overlap 30%
            overlap = 0.3
            slice_width = int(width * (1 + overlap) / 4)
            slice_height = int(height * (1 + overlap) / 3)
            
            all_detections = []
            
            # Tạo 12 lát
            for row in range(3):
                for col in range(4):
                    x = int(col * width / 4 - slice_width * overlap / 2)
                    y = int(row * height / 3 - slice_height * overlap / 2)
                    
                    # Đảm bảo tọa độ không âm
                    x = max(0, x)
                    y = max(0, y)
                    
                    # Cắt lát ảnh
                    slice_img = image[y:y+slice_height, x:x+slice_width]
                    
                    # Chạy detection trên lát
                    results = self.model(slice_img)
                    
                    # Xử lý kết quả và điều chỉnh tọa độ
                    for result in results:
                        boxes = result.boxes
                        if boxes is not None:
                            for box in boxes:
                                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                confidence = float(box.conf[0].cpu().numpy())
                                class_id = int(box.cls[0].cpu().numpy())
                                class_name = self.model.names[class_id]
                                
                                # Điều chỉnh tọa độ về ảnh gốc
                                global_x1 = int(x1 + x)
                                global_y1 = int(y1 + y)
                                global_x2 = int(x2 + x)
                                global_y2 = int(y2 + y)
                                
                                # Tạo bounding box
                                bbox = BoundingBox(
                                    x=global_x1,
                                    y=global_y1,
                                    width=global_x2 - global_x1,
                                    height=global_y2 - global_y1
                                )
                                
                                # Tạo detected object
                                obj = DetectedObject(
                                    class_name=class_name,
                                    class_id=class_id,
                                    confidence=confidence,
                                    bounding_box=bbox
                                )
                                all_detections.append(obj)
            
            # Loại bỏ duplicate detections
            filtered_detections = self._non_max_suppression(all_detections)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method="super_slicing_4x3"
            )
            
        except Exception as e:
            logger.error(f"Error in super slice detection: {e}")
            raise
    
    def _non_max_suppression(self, detections: List[DetectedObject], iou_threshold: float = 0.5) -> List[DetectedObject]:
        """
        Loại bỏ duplicate detections sử dụng Non-Maximum Suppression
        """
        if not detections:
            return []
        
        # Sắp xếp theo confidence
        detections.sort(key=lambda x: x.confidence, reverse=True)
        
        filtered = []
        
        for detection in detections:
            is_duplicate = False
            
            for filtered_detection in filtered:
                if self._calculate_iou(detection.bounding_box, filtered_detection.bounding_box) > iou_threshold:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(detection)
        
        return filtered
    
    def _calculate_iou(self, box1: BoundingBox, box2: BoundingBox) -> float:
        """
        Tính Intersection over Union (IoU) giữa 2 bounding box
        """
        # Tính tọa độ intersection
        x1 = max(box1.x, box2.x)
        y1 = max(box1.y, box2.y)
        x2 = min(box1.x + box1.width, box2.x + box2.width)
        y2 = min(box1.y + box1.height, box2.y + box2.height)
        
        # Tính diện tích intersection
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # Tính diện tích union
        area1 = box1.width * box1.height
        area2 = box2.width * box2.height
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
