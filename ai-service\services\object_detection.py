import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from pathlib import Path
import logging
from typing import List, Dict, Any, Optional, Tuple
import asyncio
import yaml
import json
import time
from datetime import datetime

from models.detection_result import DetectionResult, BoundingBox, DetectedObject

logger = logging.getLogger(__name__)

class EnhancedObjectDetectionService:
    """
    Enhanced Object Detection Service with fine-tuned furniture models
    Supports multiple models, confidence tuning, and optimized detection
    """

    def __init__(self, config_path: str = "detection_config.yaml"):
        self.config = self.load_config(config_path)
        self.models = {}
        self.current_model_name = "default"
        self.furniture_classes = self.load_furniture_classes()
        self.class_confidence_thresholds = self.config.get('class_confidence_thresholds', {})
        self.load_models()

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load detection configuration"""
        default_config = {
            'models': {
                'default': {
                    'path': 'models/yolo11s-seg.pt',
                    'type': 'segmentation',
                    'confidence': 0.25,
                    'iou': 0.45
                },
                'furniture_finetuned': {
                    'path': 'models/furniture_yolo11s_best.pt',
                    'type': 'detection',
                    'confidence': 0.3,
                    'iou': 0.5
                },
                'small_objects': {
                    'path': 'models/small_objects_yolo11s.pt',
                    'type': 'detection',
                    'confidence': 0.2,
                    'iou': 0.4
                }
            },
            'detection_settings': {
                'max_det': 300,
                'agnostic_nms': False,
                'retina_masks': True,
                'half': False,
                'device': 'auto'
            },
            'class_confidence_thresholds': {
                'cup': 0.15,
                'plate': 0.15,
                'bowl': 0.15,
                'pillow': 0.2,
                'book': 0.2,
                'clock': 0.25,
                'vase': 0.25,
                'chair': 0.3,
                'table': 0.3,
                'sofa': 0.35,
                'bed': 0.35
            },
            'small_object_enhancement': {
                'enabled': True,
                'min_size': 32,
                'scale_factors': [0.8, 1.0, 1.2],
                'tta': True  # Test Time Augmentation
            }
        }

        if Path(config_path).exists():
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                # Deep merge configs
                self._deep_merge(default_config, user_config)

        return default_config

    def _deep_merge(self, base_dict: Dict, update_dict: Dict) -> None:
        """Deep merge two dictionaries"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    def load_furniture_classes(self) -> Dict[str, str]:
        """Load furniture class mappings"""
        furniture_classes = {
            'chair': 'Chairs and Seating',
            'table': 'Tables',
            'sofa': 'Sofas and Couches',
            'bed': 'Beds',
            'desk': 'Desks',
            'cabinet': 'Cabinets and Storage',
            'shelf': 'Shelves',
            'lamp': 'Lighting',
            'pillow': 'Pillows and Cushions',
            'plate': 'Dinnerware',
            'cup': 'Drinkware',
            'bowl': 'Bowls',
            'vase': 'Decorative Items',
            'mirror': 'Mirrors',
            'curtain': 'Window Treatments',
            'rug': 'Floor Coverings',
            'plant': 'Plants and Greenery',
            'book': 'Books and Media',
            'clock': 'Clocks',
            'picture': 'Wall Art'
        }
        return furniture_classes

    def load_models(self):
        """Load all configured models"""
        for model_name, model_config in self.config['models'].items():
            try:
                model_path = model_config['path']
                if Path(model_path).exists():
                    self.models[model_name] = {
                        'model': YOLO(model_path),
                        'config': model_config
                    }
                    logger.info(f"Loaded model '{model_name}' from {model_path}")
                else:
                    logger.warning(f"Model file not found: {model_path}")
            except Exception as e:
                logger.error(f"Error loading model '{model_name}': {e}")

        # Fallback to default model if no models loaded
        if not self.models:
            try:
                self.models['fallback'] = {
                    'model': YOLO('yolo11s.pt'),
                    'config': self.config['models']['default']
                }
                logger.info("Loaded fallback YOLO model")
            except Exception as e:
                logger.error(f"Failed to load fallback model: {e}")
                raise

    def set_model(self, model_name: str) -> bool:
        """Switch to a specific model"""
        if model_name in self.models:
            self.current_model_name = model_name
            logger.info(f"Switched to model: {model_name}")
            return True
        else:
            logger.warning(f"Model '{model_name}' not available")
            return False

    def get_current_model(self) -> Tuple[YOLO, Dict[str, Any]]:
        """Get current active model and its config"""
        if self.current_model_name in self.models:
            model_info = self.models[self.current_model_name]
            return model_info['model'], model_info['config']
        else:
            # Return first available model
            first_model = next(iter(self.models.values()))
            return first_model['model'], first_model['config']

    async def detect_objects(self, image_path: str, model_name: Optional[str] = None) -> DetectionResult:
        """
        Enhanced object detection with confidence tuning and furniture focus
        """
        try:
            # Switch model if specified
            if model_name and model_name in self.models:
                original_model = self.current_model_name
                self.set_model(model_name)
            else:
                original_model = None

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Cannot read image")

            # Get current model and config
            model, model_config = self.get_current_model()

            # Run inference with model-specific settings
            results = model(
                image,
                conf=model_config.get('confidence', 0.25),
                iou=model_config.get('iou', 0.45),
                max_det=self.config['detection_settings']['max_det'],
                agnostic_nms=self.config['detection_settings']['agnostic_nms'],
                retina_masks=self.config['detection_settings']['retina_masks'],
                half=self.config['detection_settings']['half'],
                device=self.config['detection_settings']['device']
            )

            # Process results with enhanced filtering
            detected_objects = self._process_detection_results(results, image.shape)

            # Apply furniture-specific post-processing
            detected_objects = self._apply_furniture_filtering(detected_objects)

            # Restore original model if switched
            if original_model:
                self.set_model(original_model)

            return DetectionResult(
                image_path=image_path,
                detected_objects=detected_objects,
                total_objects=len(detected_objects),
                method=f"enhanced_{self.current_model_name}",
                image_size=(image.shape[1], image.shape[0])
            )

        except Exception as e:
            logger.error(f"Error in enhanced object detection: {e}")
            raise

    def _process_detection_results(self, results, image_shape: Tuple[int, int, int]) -> List[DetectedObject]:
        """Process YOLO detection results with enhanced filtering"""
        detected_objects = []

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Extract detection data
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())

                    # Get class name
                    if hasattr(result, 'names') and class_id in result.names:
                        class_name = result.names[class_id]
                    else:
                        class_name = f"class_{class_id}"

                    # Apply class-specific confidence thresholds
                    min_confidence = self.class_confidence_thresholds.get(class_name, 0.25)
                    if confidence < min_confidence:
                        continue

                    # Calculate object size for small object enhancement
                    obj_width = x2 - x1
                    obj_height = y2 - y1
                    obj_area = obj_width * obj_height

                    # Skip very small detections that might be noise
                    min_area = self.config['small_object_enhancement']['min_size'] ** 2
                    if obj_area < min_area:
                        continue

                    # Create bounding box
                    bbox = BoundingBox(
                        x=int(x1),
                        y=int(y1),
                        width=int(obj_width),
                        height=int(obj_height)
                    )

                    # Create detected object with enhanced metadata
                    obj = DetectedObject(
                        class_name=class_name,
                        class_id=class_id,
                        confidence=confidence,
                        bounding_box=bbox
                    )

                    # Add furniture category if available
                    if class_name in self.furniture_classes:
                        obj.furniture_category = self.furniture_classes[class_name]

                    detected_objects.append(obj)

        return detected_objects

    def _apply_furniture_filtering(self, detected_objects: List[DetectedObject]) -> List[DetectedObject]:
        """Apply furniture-specific filtering and enhancement"""
        # Filter to keep only furniture-related objects
        furniture_objects = []

        for obj in detected_objects:
            # Keep objects that are in our furniture classes
            if obj.class_name in self.furniture_classes:
                furniture_objects.append(obj)
            # Also keep some general objects that might be furniture-related
            elif obj.class_name in ['person', 'bottle', 'book', 'clock', 'vase']:
                furniture_objects.append(obj)

        # Sort by confidence (highest first)
        furniture_objects.sort(key=lambda x: x.confidence, reverse=True)

        # Apply additional NMS for furniture objects
        filtered_objects = self._furniture_nms(furniture_objects)

        return filtered_objects

    def _furniture_nms(self, objects: List[DetectedObject], iou_threshold: float = 0.5) -> List[DetectedObject]:
        """Furniture-specific Non-Maximum Suppression"""
        if not objects:
            return []

        # Group objects by class for class-specific NMS
        class_groups = {}
        for obj in objects:
            if obj.class_name not in class_groups:
                class_groups[obj.class_name] = []
            class_groups[obj.class_name].append(obj)

        filtered_objects = []

        for class_name, class_objects in class_groups.items():
            # Apply NMS within each class
            class_filtered = self._non_max_suppression(class_objects, iou_threshold)
            filtered_objects.extend(class_filtered)

        return filtered_objects

    async def detect_objects(self, image_path: str, model_name: Optional[str] = None) -> DetectionResult:
        """
        Nhận diện vật thể sử dụng YOLO thông thường
        """
        try:
            # Load ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            # Chạy inference
            results = self.model(image)
            
            # Xử lý kết quả
            detected_objects = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Lấy tọa độ bounding box
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0].cpu().numpy())
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = self.model.names[class_id]
                        
                        # Tạo bounding box
                        bbox = BoundingBox(
                            x=int(x1),
                            y=int(y1),
                            width=int(x2 - x1),
                            height=int(y2 - y1)
                        )
                        
                        # Tạo detected object
                        obj = DetectedObject(
                            class_name=class_name,
                            class_id=class_id,
                            confidence=confidence,
                            bounding_box=bbox
                        )
                        detected_objects.append(obj)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=detected_objects,
                total_objects=len(detected_objects)
            )
            
        except Exception as e:
            logger.error(f"Error in object detection: {e}")
            raise
    
    async def detect_objects_slice(self, image_path: str) -> DetectionResult:
        """
        Nhận diện vật thể sử dụng kỹ thuật slicing (từ YoloSlicing)
        Chia ảnh thành 4 lát (2x2) với overlap
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            height, width = image.shape[:2]
            
            # Chia ảnh thành 4 lát (2x2) với overlap 20%
            overlap = 0.2
            slice_width = int(width * (1 + overlap) / 2)
            slice_height = int(height * (1 + overlap) / 2)
            
            all_detections = []
            
            # Tạo 4 lát
            slices = [
                (0, 0, slice_width, slice_height),  # Top-left
                (int(width/2 - slice_width/2), 0, slice_width, slice_height),  # Top-right
                (0, int(height/2 - slice_height/2), slice_width, slice_height),  # Bottom-left
                (int(width/2 - slice_width/2), int(height/2 - slice_height/2), slice_width, slice_height)  # Bottom-right
            ]
            
            for i, (x, y, w, h) in enumerate(slices):
                # Cắt lát ảnh
                slice_img = image[y:y+h, x:x+w]
                
                # Chạy detection trên lát
                results = self.model(slice_img)
                
                # Xử lý kết quả và điều chỉnh tọa độ
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0].cpu().numpy())
                            class_id = int(box.cls[0].cpu().numpy())
                            class_name = self.model.names[class_id]
                            
                            # Điều chỉnh tọa độ về ảnh gốc
                            global_x1 = int(x1 + x)
                            global_y1 = int(y1 + y)
                            global_x2 = int(x2 + x)
                            global_y2 = int(y2 + y)
                            
                            # Tạo bounding box
                            bbox = BoundingBox(
                                x=global_x1,
                                y=global_y1,
                                width=global_x2 - global_x1,
                                height=global_y2 - global_y1
                            )
                            
                            # Tạo detected object
                            obj = DetectedObject(
                                class_name=class_name,
                                class_id=class_id,
                                confidence=confidence,
                                bounding_box=bbox
                            )
                            all_detections.append(obj)
            
            # Loại bỏ duplicate detections (NMS)
            filtered_detections = self._non_max_suppression(all_detections)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method="slicing_2x2"
            )
            
        except Exception as e:
            logger.error(f"Error in slice detection: {e}")
            raise
    
    async def detect_objects_super_slice(self, image_path: str) -> DetectionResult:
        """
        Nhận diện vật thể sử dụng kỹ thuật super slicing (4x3)
        Chia ảnh thành 12 lát với overlap lớn hơn
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Không thể đọc ảnh")
            
            height, width = image.shape[:2]
            
            # Chia ảnh thành 12 lát (4x3) với overlap 30%
            overlap = 0.3
            slice_width = int(width * (1 + overlap) / 4)
            slice_height = int(height * (1 + overlap) / 3)
            
            all_detections = []
            
            # Tạo 12 lát
            for row in range(3):
                for col in range(4):
                    x = int(col * width / 4 - slice_width * overlap / 2)
                    y = int(row * height / 3 - slice_height * overlap / 2)
                    
                    # Đảm bảo tọa độ không âm
                    x = max(0, x)
                    y = max(0, y)
                    
                    # Cắt lát ảnh
                    slice_img = image[y:y+slice_height, x:x+slice_width]
                    
                    # Chạy detection trên lát
                    results = self.model(slice_img)
                    
                    # Xử lý kết quả và điều chỉnh tọa độ
                    for result in results:
                        boxes = result.boxes
                        if boxes is not None:
                            for box in boxes:
                                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                confidence = float(box.conf[0].cpu().numpy())
                                class_id = int(box.cls[0].cpu().numpy())
                                class_name = self.model.names[class_id]
                                
                                # Điều chỉnh tọa độ về ảnh gốc
                                global_x1 = int(x1 + x)
                                global_y1 = int(y1 + y)
                                global_x2 = int(x2 + x)
                                global_y2 = int(y2 + y)
                                
                                # Tạo bounding box
                                bbox = BoundingBox(
                                    x=global_x1,
                                    y=global_y1,
                                    width=global_x2 - global_x1,
                                    height=global_y2 - global_y1
                                )
                                
                                # Tạo detected object
                                obj = DetectedObject(
                                    class_name=class_name,
                                    class_id=class_id,
                                    confidence=confidence,
                                    bounding_box=bbox
                                )
                                all_detections.append(obj)
            
            # Loại bỏ duplicate detections
            filtered_detections = self._non_max_suppression(all_detections)
            
            return DetectionResult(
                image_path=image_path,
                detected_objects=filtered_detections,
                total_objects=len(filtered_detections),
                method="super_slicing_4x3"
            )
            
        except Exception as e:
            logger.error(f"Error in super slice detection: {e}")
            raise
    
    def _non_max_suppression(self, detections: List[DetectedObject], iou_threshold: float = 0.5) -> List[DetectedObject]:
        """
        Loại bỏ duplicate detections sử dụng Non-Maximum Suppression
        """
        if not detections:
            return []
        
        # Sắp xếp theo confidence
        detections.sort(key=lambda x: x.confidence, reverse=True)
        
        filtered = []
        
        for detection in detections:
            is_duplicate = False
            
            for filtered_detection in filtered:
                if self._calculate_iou(detection.bounding_box, filtered_detection.bounding_box) > iou_threshold:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(detection)
        
        return filtered
    
    def _calculate_iou(self, box1: BoundingBox, box2: BoundingBox) -> float:
        """
        Tính Intersection over Union (IoU) giữa 2 bounding box
        """
        # Tính tọa độ intersection
        x1 = max(box1.x, box2.x)
        y1 = max(box1.y, box2.y)
        x2 = min(box1.x + box1.width, box2.x + box2.width)
        y2 = min(box1.y + box1.height, box2.y + box2.height)
        
        # Tính diện tích intersection
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # Tính diện tích union
        area1 = box1.width * box1.height
        area2 = box2.width * box2.height
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
