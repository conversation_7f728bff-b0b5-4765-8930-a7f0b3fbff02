# HomeObjects-3K Furniture Detection Dataset Configuration
# Optimized for e-commerce furniture detection with focus on small objects

# Dataset paths
path: ../datasets/HomeObjects-3K
train: images/train
val: images/val
test: images/test

# Number of furniture-focused classes (filtered from original 77 classes)
nc: 25

# Furniture-focused class names (mapped from HomeObjects-3K)
names:
  0: bed
  1: bench
  2: blanket
  3: book
  4: bookshelf
  5: bottle
  6: bowl
  7: box
  8: cabinet
  9: carpet
  10: chair
  11: clock
  12: couch
  13: cup
  14: curtain
  15: cushion
  16: desk
  17: drawer
  18: dresser
  19: lamp
  20: mirror
  21: picture
  22: plant
  23: plate
  24: pot
  25: potted_plant
  26: rug
  27: shelf
  28: stool
  29: table
  30: towel
  31: vase

# Class mapping from original HomeObjects-3K to furniture categories
class_mapping:
  # Large furniture
  bed: "Beds and sleeping furniture"
  couch: "Sofas, couches, sectionals" 
  chair: "All types of chairs and seating"
  table: "Tables of all sizes and types"
  desk: "Desks and work surfaces"
  
  # Storage furniture
  cabinet: "Cabinets and storage units"
  bookshelf: "Bookshelves and display units"
  shelf: "Wall shelves and floating shelves"
  drawer: "Drawers and drawer units"
  dresser: "Dressers and chest of drawers"
  
  # Lighting and decor
  lamp: "All types of lamps and lighting"
  mirror: "Mirrors and reflective surfaces"
  picture: "Wall art and framed pictures"
  clock: "Clocks and timepieces"
  
  # Textiles and soft furnishings
  curtain: "Curtains and window treatments"
  carpet: "Carpets and large floor coverings"
  rug: "Area rugs and small floor coverings"
  blanket: "Blankets and throws"
  cushion: "Cushions and pillows"
  towel: "Towels and bathroom textiles"
  
  # Small objects and accessories
  vase: "Vases and decorative containers"
  plant: "Plants and greenery"
  potted_plant: "Potted plants and planters"
  book: "Books and reading materials"
  
  # Tableware (small objects focus)
  plate: "Plates and dinnerware"
  bowl: "Bowls and serving dishes"
  cup: "Cups, mugs, and drinkware"
  bottle: "Bottles and containers"
  pot: "Pots and cookware"
  
  # Other furniture
  bench: "Benches and seating"
  stool: "Stools and small seating"
  box: "Storage boxes and containers"

# Training optimization for small objects
small_object_classes:
  - cup
  - plate
  - bowl
  - bottle
  - book
  - clock
  - vase
  - cushion
  - pot

# Large furniture classes
large_object_classes:
  - bed
  - couch
  - table
  - desk
  - cabinet
  - bookshelf
  - dresser

# Medium furniture classes  
medium_object_classes:
  - chair
  - lamp
  - mirror
  - shelf
  - stool
  - bench

# Textile classes
textile_classes:
  - curtain
  - carpet
  - rug
  - blanket
  - towel

# Dataset statistics
dataset_stats:
  total_classes: 32
  small_objects: 9
  medium_objects: 6  
  large_objects: 7
  textiles: 5
  other: 5

# Training recommendations
training_notes:
  - "Use higher class weights for small objects (cup, plate, bowl)"
  - "Apply copy-paste augmentation for small object detection"
  - "Use multi-scale training for better small object detection"
  - "Consider focal loss for class imbalance"
  - "Use higher IoU thresholds for precise furniture detection"
