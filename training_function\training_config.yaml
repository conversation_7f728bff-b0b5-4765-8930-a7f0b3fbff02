# Advanced Training Configuration for Furniture Detection Model
# Optimized for HomeObjects-3K dataset and furniture detection

model:
  base_model: 'yolo11s.pt'  # Base YOLOv11 small model
  architecture: 'yolo11s'
  input_size: 640

training:
  epochs: 300              # Increased for better convergence
  batch_size: 16           # Adjust based on GPU memory
  learning_rate: 0.001     # Initial learning rate
  patience: 50             # Early stopping patience
  save_period: 10          # Save checkpoint every N epochs
  
data:
  dataset_path: 'datasets/HomeObjects-3K'
  yaml_file: 'furniture_data.yaml'
  train_split: 0.8
  val_split: 0.15
  test_split: 0.05
  
# Data augmentation optimized for furniture detection
augmentation:
  mosaic: 1.0              # Mosaic augmentation probability
  mixup: 0.15              # MixUp augmentation probability
  copy_paste: 0.3          # Copy-paste augmentation for small objects
  hsv_h: 0.015             # HSV hue augmentation
  hsv_s: 0.7               # HSV saturation augmentation
  hsv_v: 0.4               # HSV value augmentation
  degrees: 5.0             # Rotation degrees
  translate: 0.1           # Translation fraction
  scale: 0.5               # Scale variation
  shear: 2.0               # Shear degrees
  perspective: 0.0001      # Perspective transformation
  flipud: 0.0              # Vertical flip probability
  fliplr: 0.5              # Horizontal flip probability
  
# Optimization settings
optimization:
  optimizer: 'AdamW'       # Optimizer type
  momentum: 0.937          # SGD momentum
  weight_decay: 0.0005     # Weight decay
  warmup_epochs: 3         # Warmup epochs
  warmup_momentum: 0.8     # Warmup momentum
  warmup_bias_lr: 0.1      # Warmup bias learning rate

# Loss function weights
loss:
  box: 7.5                 # Box loss weight
  cls: 0.5                 # Classification loss weight
  dfl: 1.5                 # Distribution focal loss weight

# Model-specific settings
model_settings:
  nc: 20                   # Number of classes (furniture categories)
  depth_multiple: 0.33     # Model depth multiple
  width_multiple: 0.50     # Model width multiple
  max_channels: 1024       # Maximum channels

# Hardware settings
hardware:
  device: 'auto'           # Auto-detect GPU/CPU
  workers: 8               # Number of dataloader workers
  
# Validation settings
validation:
  save_json: true          # Save validation results as JSON
  save_hybrid: false       # Save hybrid version of labels
  conf: 0.001              # Confidence threshold for validation
  iou: 0.6                 # IoU threshold for NMS during validation
  max_det: 300             # Maximum detections per image
  half: false              # Use half precision for validation
  
# Testing settings  
testing:
  conf_threshold: 0.25     # Confidence threshold for testing
  iou_threshold: 0.45      # IoU threshold for NMS during testing
  max_det: 300             # Maximum detections per image
  agnostic_nms: false      # Class-agnostic NMS
  
# Logging and monitoring
logging:
  log_level: 'INFO'
  save_dir: 'runs/train'
  project_name: 'furniture-detection'
  experiment_name: 'homeobjects-3k'
  
# Advanced features
advanced:
  multi_scale: true        # Multi-scale training
  label_smoothing: 0.0     # Label smoothing epsilon
  nbs: 64                  # Nominal batch size for normalization
  overlap_mask: true       # Overlap masks during training
  mask_ratio: 4            # Mask downsample ratio
  dropout: 0.0             # Dropout rate
  
# Class names for furniture detection (HomeObjects-3K + extensions)
class_names:
  - 'chair'
  - 'table' 
  - 'sofa'
  - 'bed'
  - 'desk'
  - 'cabinet'
  - 'shelf'
  - 'lamp'
  - 'pillow'
  - 'plate'
  - 'cup'
  - 'bowl'
  - 'vase'
  - 'mirror'
  - 'curtain'
  - 'rug'
  - 'plant'
  - 'book'
  - 'clock'
  - 'picture'

# Class weights for imbalanced dataset (optional)
class_weights:
  chair: 1.0
  table: 1.0
  sofa: 1.2
  bed: 1.1
  desk: 1.0
  cabinet: 1.0
  shelf: 1.0
  lamp: 1.3
  pillow: 2.0      # Higher weight for small objects
  plate: 2.5       # Higher weight for small objects
  cup: 2.5         # Higher weight for small objects
  bowl: 2.0        # Higher weight for small objects
  vase: 1.5
  mirror: 1.2
  curtain: 1.1
  rug: 1.0
  plant: 1.2
  book: 1.8        # Higher weight for small objects
  clock: 1.5
  picture: 1.3
