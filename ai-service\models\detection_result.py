from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class BoundingBox(BaseModel):
    """Model cho bounding box"""
    x: int
    y: int
    width: int
    height: int

class DetectedObject(BaseModel):
    """Model cho vật thể đư<PERSON>c nhận diện"""
    class_name: str
    class_id: int
    confidence: float
    bounding_box: BoundingBox

class DetectionResult(BaseModel):
    """Model cho kết quả nhận diện"""
    image_path: str
    detected_objects: List[DetectedObject]
    total_objects: int
    method: Optional[str] = "standard"
    processing_time: Optional[float] = None
    timestamp: Optional[datetime] = None

class DetectionRequest(BaseModel):
    """Model cho request nhận diện"""
    image_url: Optional[str] = None
    use_slicing: bool = False
    use_super_slicing: bool = False
    confidence_threshold: float = 0.5
    iou_threshold: float = 0.5

class DetectionResponse(BaseModel):
    """Model cho response nhận diện"""
    success: bool
    message: str
    data: Optional[DetectionResult] = None
    error: Optional[str] = None
