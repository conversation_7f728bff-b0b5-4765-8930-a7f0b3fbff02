# 🏠 Furniture Store with AI Object Detection

Một hệ thống thương mại điện tử bán nội thất tích hợp AI để nhận diện đối tượng từ hình ảnh.

## 🌟 Tính năng chính

### 🤖 AI Object Detection
- Nhận diện đồ nội thất từ hình ảnh sử dụng YOLO v8
- Hỗ trợ nhiều kỹ thuật: Standard, Slicing, Super Slicing (4x3)
- Gợi ý sản phẩm dựa trên kết quả nhận diện
- L<PERSON><PERSON> lịch sử nhận diện và thống kê

### 🛍️ E-commerce Features
- Quản lý sản phẩm và danh mục với hình ảnh
- Tìm kiếm và lọc sản phẩm thông minh
- Giỏ hàng và thanh toán đa phương thức
- Quản lý đơn hàng với trạng thái real-time
- <PERSON><PERSON> thống người dùng và xác thực JWT
- Gợi ý sản phẩm cá nhân hóa

### 🏗️ Architecture
- Microservices architecture với 5 services
- API Gateway pattern với load balancing
- Event-driven communication qua RabbitMQ
- Containerized deployment với Docker
- Monitoring và logging tập trung

## 🛠️ Tech Stack

### Backend Services
- **AI Service**: Python 3.11, FastAPI, YOLO v8, OpenCV, Ultralytics
- **Product Service**: Node.js 18, Express, PostgreSQL, Redis
- **User Service**: Node.js 18, Express, JWT, bcrypt
- **Order Service**: Node.js 18, Express, RabbitMQ
- **API Gateway**: Node.js 18, Express, http-proxy-middleware

### Frontend
- **Web App**: React 18, TypeScript, Tailwind CSS, Framer Motion
- **State Management**: Context API
- **HTTP Client**: Axios
- **UI Components**: Headless UI, Heroicons

### Infrastructure
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Message Queue**: RabbitMQ 3
- **Monitoring**: Prometheus, Grafana
- **Containerization**: Docker, Docker Compose

## 🚀 Quick Start

### Prerequisites
- **Docker Desktop** (bao gồm Docker Compose)
- **Node.js** 18+ và **npm** 8+
- **Python** 3.11+ (cho AI service)
- **Git**
- 8GB RAM khuyến nghị

### 🎯 Cách 1: Sử dụng NPM Scripts (Khuyến nghị)

1. **Clone repository**
```bash
git clone <repository-url>
cd furniture-store
```

2. **Cài đặt tất cả dependencies**
```bash
npm run install:all
```

3. **Khởi động toàn bộ hệ thống**
```bash
npm start
```

### 🖥️ Cách 2: Sử dụng Windows Batch Scripts

1. **Cài đặt dependencies**
```cmd
install-all.bat
```

2. **Khởi động hệ thống**
```cmd
start.bat
```

### 🗄️ Database Seeding (Optional)

Sau khi hệ thống đã khởi động, bạn có thể nhập dữ liệu mẫu vào database.

1. **Chạy script seed:**
```bash
npm run db:seed
```
Thao tác này sẽ xóa dữ liệu hiện có và điền vào các danh mục và sản phẩm mẫu.

### 🔧 Development Mode

Để chạy các service riêng lẻ trong chế độ development:

```bash
# Tất cả scripts NPM
npm run start:dev          # Docker với hot reload
npm run dev:frontend       # Chỉ frontend
npm run dev:api-gateway    # Chỉ API Gateway
npm run dev:user-service   # Chỉ User Service
npm run dev:product-service # Chỉ Product Service
npm run dev:order-service  # Chỉ Order Service
npm run dev:ai             # Chỉ AI Service

# Hoặc Windows batch
start-dev.bat              # Tất cả services với concurrently
```

### 📦 Available NPM Scripts

| Script | Mô tả |
|--------|-------|
| `npm run install:all` | Cài đặt tất cả dependencies |
| `npm start` | Khởi động toàn bộ hệ thống với Docker |
| `npm run docker:down` | Dừng tất cả services |
| `npm run docker:logs` | Xem logs của tất cả services |
| `npm run docker:restart` | Restart toàn bộ hệ thống |
| `npm run build` | Build production |
| `npm test` | Chạy tất cả tests |
| `npm run clean` | Dọn dẹp Docker containers và images |
| `npm run setup` | Setup hoàn chỉnh (install + env + model) |
| `npm run db:seed` | Nhập dữ liệu mẫu vào database |

### 🌐 Access URLs

Sau khi khởi động thành công:
- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8000
- **Grafana Dashboard**: http://localhost:3001 (admin/admin)
- **RabbitMQ Management**: http://localhost:15672 (admin/password123)
- **Prometheus**: http://localhost:9090

### 🧪 Testing

```bash
npm test                    # Chạy tất cả tests
npm run test:frontend       # Test frontend
npm run test:services       # Test tất cả backend services
```

## 📊 Services Overview

| Service | Port | Description | Tech Stack |
|---------|------|-------------|------------|
| Frontend | 3000 | React web application | React, TypeScript, Tailwind |
| API Gateway | 8000 | Request routing & load balancing | Node.js, Express |
| AI Service | 8001 | Object detection & AI features | Python, FastAPI, YOLO |
| Product Service | 8002 | Product & category management | Node.js, Express, PostgreSQL |
| User Service | 8003 | Authentication & user management | Node.js, Express, JWT |
| Order Service | 8004 | Cart, orders & payments | Node.js, Express, RabbitMQ |

## 🔧 Infrastructure Services

| Service | Port | Credentials | Description |
|---------|------|-------------|-------------|
| PostgreSQL | 5432 | admin/password123 | Primary database |
| Redis | 6379 | - | Caching layer |
| RabbitMQ | 5672, 15672 | admin/password123 | Message queue |
| Prometheus | 9090 | - | Metrics collection |
| Grafana | 3001 | admin/admin | Monitoring dashboard |

## 🎯 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Products
- `GET /api/products` - List products
- `GET /api/products/:id` - Get product details
- `GET /api/categories` - List categories
- `GET /api/search` - Search products

### AI Detection
- `POST /api/ai/detect` - Standard object detection
- `POST /api/ai/detect-slice` - Slicing detection
- `POST /api/ai/detect-super-slice` - Super slicing detection

### Cart & Orders
- `GET /api/cart` - Get user cart
- `POST /api/cart/items` - Add item to cart
- `GET /api/orders` - List user orders
- `POST /api/orders` - Create order

## 🔍 Development

### Setup Development Environment

```bash
chmod +x scripts/dev.sh
./scripts/dev.sh
```

### Running Individual Services

1. **Start infrastructure**
```bash
docker-compose up postgres redis rabbitmq -d
```

2. **AI Service**
```bash
cd ai-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

3. **Product Service**
```bash
cd product-service
npm install
npm run dev
```

4. **Frontend**
```bash
cd web-frontend
npm install
npm start
```

### Database Schema

Xem file `database/schema.sql` để biết chi tiết về cấu trúc database.

## 📈 Monitoring & Logging

### Prometheus Metrics
- Service health checks
- Request rates và latency
- Error rates
- Resource usage

### Grafana Dashboards
- System overview
- Service-specific metrics
- AI detection performance
- Business metrics

### Logs
```bash
# Xem logs của tất cả services
docker-compose logs -f

# Xem logs của service cụ thể
docker-compose logs -f ai-service
```

## 🛡️ Security Features

- JWT-based authentication
- Password hashing với bcrypt
- Rate limiting
- CORS protection
- Input validation
- SQL injection prevention

## 🎨 Frontend Features

- Responsive design
- Dark/Light mode support
- Image upload với drag & drop
- Real-time cart updates
- Search với autocomplete
- Product filtering
- AI detection visualization

## 📱 Mobile Support

Frontend được thiết kế responsive và hỗ trợ đầy đủ trên mobile devices.

## 🔄 CI/CD

Dự án sẵn sàng cho CI/CD với:
- Docker containerization
- Health checks
- Environment configuration
- Automated testing scripts

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết chi tiết.

## 🆘 Troubleshooting

### Common Issues

1. **Services không start**
```bash
docker-compose down
docker-compose up --build
```

2. **Database connection errors**
```bash
docker-compose restart postgres
```

3. **AI model không tải được**
```bash
# Download manual
curl -L "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt" -o ai-service/models/yolov8n.pt
```

4. **Frontend không connect được API**
- Kiểm tra REACT_APP_API_URL trong .env
- Đảm bảo API Gateway đang chạy

### Support

Tạo issue trên GitHub repository để được hỗ trợ.

## 🎉 Demo

Truy cập http://localhost:3000 sau khi start services để xem demo đầy đủ.

## 📋 Project Structure

```
furniture-store/
├── ai-service/              # AI Object Detection Service
│   ├── src/
│   ├── models/             # YOLO models
│   ├── uploads/            # Uploaded images
│   ├── requirements.txt
│   └── Dockerfile
├── product-service/         # Product Management Service
│   ├── src/
│   │   ├── routes/
│   │   └── config/
│   ├── package.json
│   └── Dockerfile
├── user-service/           # User Authentication Service
│   ├── src/
│   │   ├── routes/
│   │   ├── middleware/
│   │   └── utils/
│   ├── package.json
│   └── Dockerfile
├── order-service/          # Order Management Service
│   ├── src/
│   │   └── routes/
│   ├── package.json
│   └── Dockerfile
├── api-gateway/            # API Gateway
│   ├── src/
│   ├── package.json
│   └── Dockerfile
├── web-frontend/           # React Frontend
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── contexts/
│   │   └── services/
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── database/               # Database Schema
│   └── schema.sql
├── monitoring/             # Monitoring Configuration
│   └── prometheus.yml
├── scripts/                # Utility Scripts
│   ├── start.sh
│   ├── stop.sh
│   ├── dev.sh
│   └── test.sh
├── docker-compose.yml      # Docker Compose Configuration
├── .env.example           # Environment Variables Template
└── README.md              # This file
```
## Dữ liệu Dataset

Bộ dữ liệu cuối cùng tổng hợp từ HomeObjects-3K, SUNRGBD và ADE20K

**Tải về Dataset:** Hãy tải bộ dữ liệu được xử lý từ link sau:
* [**Tải xuống furniture_dataset_prep.zip**](https://drive.google.com/file/d/1dJ-zkXxVdNSGXbOP8LHPmOgprAjyHJ2Y/view?usp=sharing)

**Sử dụng:** Sau khi tải về, giải nén và cho thư mục final_dataset (hay mini_dataset khi bạn cần dữ liệu nhỏ) vào thư mục gốc



