# Build stage
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./

# Cài đặt dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Fix ajv and ajv-keywords compatibility issue and build app
RUN npm install ajv@^6.12.6 ajv-keywords@^3.5.2 --legacy-peer-deps && npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]
