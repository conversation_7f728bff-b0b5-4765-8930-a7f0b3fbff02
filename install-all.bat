@echo off
echo 🏠 Installing all dependencies for Furniture Store...

echo.
echo 📦 Installing frontend dependencies...
cd web-frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    exit /b 1
)
cd ..

echo.
echo 📦 Installing API Gateway dependencies...
cd api-gateway
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install API Gateway dependencies
    exit /b 1
)
cd ..

echo.
echo 📦 Installing User Service dependencies...
cd user-service
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install User Service dependencies
    exit /b 1
)
cd ..

echo.
echo 📦 Installing Product Service dependencies...
cd product-service
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Product Service dependencies
    exit /b 1
)
cd ..

echo.
echo 📦 Installing Order Service dependencies...
cd order-service
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Order Service dependencies
    exit /b 1
)
cd ..

echo.
echo 🐍 Installing AI Service dependencies...
cd ai-service
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install AI Service dependencies
    exit /b 1
)
cd ..

echo.
echo ✅ All dependencies installed successfully!
echo.
echo 🚀 Ready to start the application with: npm start
