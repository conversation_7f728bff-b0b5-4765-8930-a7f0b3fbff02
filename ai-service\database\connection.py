import asyncpg
import os
import logging
from typing import Optional
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseConnection:
    """
    Quản lý kết nối database cho AI Service
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.database_url = os.getenv('DATABASE_URL', 'postgresql://admin:password123@localhost:5432/furniture_store')
    
    async def connect(self):
        """
        Tạo connection pool
        """
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connection pool created successfully")
        except Exception as e:
            logger.error(f"Error creating database connection pool: {e}")
            raise
    
    async def disconnect(self):
        """
        Đóng connection pool
        """
        if self.pool:
            await self.pool.close()
            logger.info("Database connection pool closed")
    
    async def save_detection_history(self, user_id: Optional[int], image_url: str, 
                                   detection_result: dict, processing_time: float, method: str):
        """
        <PERSON><PERSON><PERSON> <PERSON><PERSON> sử nhận diện vào database
        """
        try:
            async with self.pool.acquire() as connection:
                query = """
                    INSERT INTO detection_history 
                    (user_id, image_url, detection_result, detected_objects_count, processing_time, method, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id
                """
                
                detected_objects_count = len(detection_result.get('detected_objects', []))
                
                result = await connection.fetchrow(
                    query,
                    user_id,
                    image_url,
                    json.dumps(detection_result),
                    detected_objects_count,
                    processing_time,
                    method,
                    datetime.now()
                )
                
                logger.info(f"Detection history saved with ID: {result['id']}")
                return result['id']
                
        except Exception as e:
            logger.error(f"Error saving detection history: {e}")
            raise
    
    async def get_detection_history(self, user_id: int, limit: int = 10):
        """
        Lấy lịch sử nhận diện của user
        """
        try:
            async with self.pool.acquire() as connection:
                query = """
                    SELECT id, image_url, detection_result, detected_objects_count, 
                           processing_time, method, created_at
                    FROM detection_history
                    WHERE user_id = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                """
                
                rows = await connection.fetch(query, user_id, limit)
                
                history = []
                for row in rows:
                    history.append({
                        'id': row['id'],
                        'image_url': row['image_url'],
                        'detection_result': json.loads(row['detection_result']),
                        'detected_objects_count': row['detected_objects_count'],
                        'processing_time': float(row['processing_time']),
                        'method': row['method'],
                        'created_at': row['created_at'].isoformat()
                    })
                
                return history
                
        except Exception as e:
            logger.error(f"Error getting detection history: {e}")
            raise
    
    async def save_product_recommendation(self, user_id: int, product_id: int, 
                                        detected_object_class: str, confidence_score: float,
                                        recommendation_reason: str):
        """
        Lưu gợi ý sản phẩm dựa trên kết quả nhận diện
        """
        try:
            async with self.pool.acquire() as connection:
                query = """
                    INSERT INTO product_recommendations 
                    (user_id, product_id, detected_object_class, confidence_score, recommendation_reason, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING id
                """
                
                result = await connection.fetchrow(
                    query,
                    user_id,
                    product_id,
                    detected_object_class,
                    confidence_score,
                    recommendation_reason,
                    datetime.now()
                )
                
                logger.info(f"Product recommendation saved with ID: {result['id']}")
                return result['id']
                
        except Exception as e:
            logger.error(f"Error saving product recommendation: {e}")
            raise
    
    async def get_detection_stats(self):
        """
        Lấy thống kê về việc nhận diện
        """
        try:
            async with self.pool.acquire() as connection:
                # Tổng số lần nhận diện
                total_detections = await connection.fetchval(
                    "SELECT COUNT(*) FROM detection_history"
                )
                
                # Số lượng object được nhận diện
                total_objects = await connection.fetchval(
                    "SELECT SUM(detected_objects_count) FROM detection_history"
                )
                
                # Thời gian xử lý trung bình
                avg_processing_time = await connection.fetchval(
                    "SELECT AVG(processing_time) FROM detection_history"
                )
                
                # Phương pháp được sử dụng nhiều nhất
                popular_methods = await connection.fetch(
                    """
                    SELECT method, COUNT(*) as count
                    FROM detection_history
                    GROUP BY method
                    ORDER BY count DESC
                    LIMIT 5
                    """
                )
                
                return {
                    'total_detections': total_detections or 0,
                    'total_objects': total_objects or 0,
                    'avg_processing_time': float(avg_processing_time) if avg_processing_time else 0.0,
                    'popular_methods': [
                        {'method': row['method'], 'count': row['count']}
                        for row in popular_methods
                    ]
                }
                
        except Exception as e:
            logger.error(f"Error getting detection stats: {e}")
            raise

# Global database connection instance
db_connection = DatabaseConnection()

async def get_database_connection():
    """
    Lấy database connection instance
    """
    if db_connection.pool is None:
        await db_connection.connect()
    return db_connection
