const express = require('express');
const router = express.Router();
const { pool, redisClient } = require('../config/database');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const createOrderSchema = Joi.object({
  shipping_address: Joi.string().required(),
  payment_method: Joi.string().valid('credit_card', 'paypal', 'bank_transfer', 'cash_on_delivery').required(),
  notes: Joi.string().allow('')
});

const updateOrderStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled').required(),
  notes: Joi.string().allow('')
});

// Helper function to generate order number
function generateOrderNumber() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD${timestamp.slice(-6)}${random}`;
}

// GET /api/orders - <PERSON><PERSON><PERSON> danh sách đơn hàng
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    const { 
      page = 1, 
      limit = 10, 
      status,
      user_id,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    let query = `
      SELECT o.id, o.order_number, o.user_id, o.status, o.total_amount,
             o.shipping_address, o.payment_method, o.payment_status,
             o.notes, o.created_at, o.updated_at,
             u.first_name, u.last_name, u.email,
             COUNT(oi.id) as item_count
      FROM orders o
      JOIN users u ON o.user_id = u.id
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE 1=1
    `;
    
    const queryParams = [];
    let paramIndex = 1;

    // Regular users can only see their own orders
    if (userRole === 'customer') {
      query += ` AND o.user_id = $${paramIndex}`;
      queryParams.push(userId);
      paramIndex++;
    } else if (user_id && userRole === 'admin') {
      // Admin can filter by specific user
      query += ` AND o.user_id = $${paramIndex}`;
      queryParams.push(user_id);
      paramIndex++;
    }

    if (status) {
      query += ` AND o.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    query += ` GROUP BY o.id, u.first_name, u.last_name, u.email`;

    // Add sorting
    const allowedSortFields = ['created_at', 'total_amount', 'status', 'order_number'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY o.${sortField} ${sortDirection}`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      WHERE 1=1
    `;
    
    const countParams = [];
    let countParamIndex = 1;

    if (userRole === 'customer') {
      countQuery += ` AND o.user_id = $${countParamIndex}`;
      countParams.push(userId);
      countParamIndex++;
    } else if (user_id && userRole === 'admin') {
      countQuery += ` AND o.user_id = $${countParamIndex}`;
      countParams.push(user_id);
      countParamIndex++;
    }

    if (status) {
      countQuery += ` AND o.status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    const [ordersResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const orders = ordersResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/orders/:id - Lấy chi tiết đơn hàng
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;

    // Build query based on user role
    let orderQuery = `
      SELECT o.*, u.first_name, u.last_name, u.email, u.phone
      FROM orders o
      JOIN users u ON o.user_id = u.id
      WHERE o.id = $1
    `;

    const queryParams = [id];

    // Regular users can only see their own orders
    if (userRole === 'customer') {
      orderQuery += ` AND o.user_id = $2`;
      queryParams.push(userId);
    }

    const orderResult = await pool.query(orderQuery, queryParams);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsQuery = `
      SELECT oi.*, p.name, p.description,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as image_url
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
      ORDER BY oi.id
    `;

    const itemsResult = await pool.query(itemsQuery, [id]);
    order.items = itemsResult.rows;

    res.json(order);

  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/orders - Tạo đơn hàng từ giỏ hàng
router.post('/', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const userId = req.user.userId;
    const { error, value } = createOrderSchema.validate(req.body);
    
    if (error) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: error.details[0].message });
    }

    const { shipping_address, payment_method, notes } = value;

    // Get cart items
    const cartQuery = `
      SELECT ci.product_id, ci.quantity, p.name, p.price, p.sale_price, p.stock_quantity
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      WHERE ci.user_id = $1 AND p.is_active = true
    `;

    const cartResult = await client.query(cartQuery, [userId]);

    if (cartResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Cart is empty' });
    }

    const cartItems = cartResult.rows;

    // Check stock availability for all items
    for (const item of cartItems) {
      if (item.stock_quantity < item.quantity) {
        await client.query('ROLLBACK');
        return res.status(400).json({ 
          error: `Insufficient stock for ${item.name}`,
          available_quantity: item.stock_quantity
        });
      }
    }

    // Calculate total amount
    let totalAmount = 0;
    cartItems.forEach(item => {
      const price = item.sale_price || item.price;
      totalAmount += parseFloat(price) * item.quantity;
    });

    // Add tax (10%)
    totalAmount = totalAmount * 1.1;

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Create order
    const orderQuery = `
      INSERT INTO orders (user_id, order_number, status, total_amount, shipping_address, payment_method, notes)
      VALUES ($1, $2, 'pending', $3, $4, $5, $6)
      RETURNING *
    `;

    const orderResult = await client.query(orderQuery, [
      userId, orderNumber, totalAmount, shipping_address, payment_method, notes
    ]);

    const order = orderResult.rows[0];

    // Create order items and update stock
    for (const item of cartItems) {
      const price = item.sale_price || item.price;
      
      // Insert order item
      await client.query(
        'INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES ($1, $2, $3, $4, $5)',
        [order.id, item.product_id, item.quantity, price, parseFloat(price) * item.quantity]
      );

      // Update product stock
      await client.query(
        'UPDATE products SET stock_quantity = stock_quantity - $1 WHERE id = $2',
        [item.quantity, item.product_id]
      );
    }

    // Clear cart
    await client.query('DELETE FROM cart_items WHERE user_id = $1', [userId]);

    await client.query('COMMIT');

    // Clear cart cache
    await redisClient.del(`cart:${userId}`);

    res.status(201).json({
      message: 'Order created successfully',
      order: order
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating order:', error);
    res.status(500).json({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

// PUT /api/orders/:id/status - Cập nhật trạng thái đơn hàng (Admin/Staff only)
router.put('/:id/status', authenticateToken, authorizeRoles('admin', 'staff'), async (req, res) => {
  try {
    const { id } = req.params;
    const { error, value } = updateOrderStatusSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { status, notes } = value;

    const query = `
      UPDATE orders 
      SET status = $1, notes = COALESCE($2, notes), updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `;

    const result = await pool.query(query, [status, notes, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({
      message: 'Order status updated successfully',
      order: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/orders/:id - Hủy đơn hàng
router.delete('/:id', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;

    // Get order details
    let orderQuery = `
      SELECT o.*, oi.product_id, oi.quantity
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.id = $1
    `;

    const queryParams = [id];

    // Regular users can only cancel their own orders
    if (userRole === 'customer') {
      orderQuery += ` AND o.user_id = $2`;
      queryParams.push(userId);
    }

    const orderResult = await client.query(orderQuery, queryParams);

    if (orderResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Check if order can be cancelled
    if (['shipped', 'delivered', 'cancelled'].includes(order.status)) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Order cannot be cancelled' });
    }

    // Restore stock for all order items
    const orderItems = orderResult.rows;
    for (const item of orderItems) {
      if (item.product_id) {
        await client.query(
          'UPDATE products SET stock_quantity = stock_quantity + $1 WHERE id = $2',
          [item.quantity, item.product_id]
        );
      }
    }

    // Update order status to cancelled
    await client.query(
      'UPDATE orders SET status = \'cancelled\', updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );

    await client.query('COMMIT');

    res.json({ message: 'Order cancelled successfully' });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error cancelling order:', error);
    res.status(500).json({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

module.exports = router;
