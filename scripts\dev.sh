#!/bin/bash

# Development Environment Setup
echo "🔧 Setting up development environment..."

# Install dependencies for each service
echo "📦 Installing dependencies..."

# AI Service
if [ -d "ai-service" ]; then
    echo "Installing AI Service dependencies..."
    cd ai-service
    pip install -r requirements.txt
    cd ..
fi

# Product Service
if [ -d "product-service" ]; then
    echo "Installing Product Service dependencies..."
    cd product-service
    npm install
    cd ..
fi

# User Service
if [ -d "user-service" ]; then
    echo "Installing User Service dependencies..."
    cd user-service
    npm install
    cd ..
fi

# Order Service
if [ -d "order-service" ]; then
    echo "Installing Order Service dependencies..."
    cd order-service
    npm install
    cd ..
fi

# API Gateway
if [ -d "api-gateway" ]; then
    echo "Installing API Gateway dependencies..."
    cd api-gateway
    npm install
    cd ..
fi

# Web Frontend
if [ -d "web-frontend" ]; then
    echo "Installing Frontend dependencies..."
    cd web-frontend
    npm install
    cd ..
fi

echo "✅ Development environment setup complete!"
echo ""
echo "🚀 To start development:"
echo "   • Database: docker-compose up postgres redis rabbitmq -d"
echo "   • Services: Run each service individually in development mode"
echo "   • Frontend: cd web-frontend && npm start"
