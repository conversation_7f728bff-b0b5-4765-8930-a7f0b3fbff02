const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { createProxyMiddleware } = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'api-gateway',
    timestamp: new Date().toISOString()
  });
});

// Proxy routes
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://ai-service:8001';
const PRODUCT_SERVICE_URL = process.env.PRODUCT_SERVICE_URL || 'http://product-service:8002';
const USER_SERVICE_URL = process.env.USER_SERVICE_URL || 'http://user-service:8003';
const ORDER_SERVICE_URL = process.env.ORDER_SERVICE_URL || 'http://order-service:8004';

// AI Service proxy
app.use('/api/ai', createProxyMiddleware({
  target: AI_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/ai': '/'
  },
  onError: (err, req, res) => {
    console.error('AI Service Error:', err);
    res.status(503).json({ error: 'AI Service unavailable' });
  }
}));

// Product Service proxy
app.use('/api/products', createProxyMiddleware({
  target: PRODUCT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/products': '/api/products'
  },
  onError: (err, req, res) => {
    console.error('Product Service Error:', err);
    res.status(503).json({ error: 'Product Service unavailable' });
  }
}));

// User Service proxy - Auth routes
app.use('/api/auth', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/auth': '/api/auth'
  },
  onError: (err, req, res) => {
    console.error('User Service Error:', err);
    res.status(503).json({ error: 'User Service unavailable' });
  }
}));

// User Service proxy - User management
app.use('/api/users', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/users': '/api/users'
  },
  onError: (err, req, res) => {
    console.error('User Service Error:', err);
    res.status(503).json({ error: 'User Service unavailable' });
  }
}));

// User Service proxy - Profile routes
app.use('/api/profile', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/profile': '/api/profile'
  },
  onError: (err, req, res) => {
    console.error('User Service Error:', err);
    res.status(503).json({ error: 'User Service unavailable' });
  }
}));

// Product Service proxy - Categories
app.use('/api/categories', createProxyMiddleware({
  target: PRODUCT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/categories': '/api/categories'
  },
  onError: (err, req, res) => {
    console.error('Product Service Error:', err);
    res.status(503).json({ error: 'Product Service unavailable' });
  }
}));

// Product Service proxy - Search
app.use('/api/search', createProxyMiddleware({
  target: PRODUCT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/search': '/api/search'
  },
  onError: (err, req, res) => {
    console.error('Product Service Error:', err);
    res.status(503).json({ error: 'Product Service unavailable' });
  }
}));

// Order Service proxy - Orders
app.use('/api/orders', createProxyMiddleware({
  target: ORDER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/orders': '/api/orders'
  },
  onError: (err, req, res) => {
    console.error('Order Service Error:', err);
    res.status(503).json({ error: 'Order Service unavailable' });
  }
}));

// Order Service proxy - Cart
app.use('/api/cart', createProxyMiddleware({
  target: ORDER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/cart': '/api/cart'
  },
  onError: (err, req, res) => {
    console.error('Order Service Error:', err);
    res.status(503).json({ error: 'Order Service unavailable' });
  }
}));

// Order Service proxy - Payments
app.use('/api/payments', createProxyMiddleware({
  target: ORDER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/payments': '/api/payments'
  },
  onError: (err, req, res) => {
    console.error('Order Service Error:', err);
    res.status(503).json({ error: 'Order Service unavailable' });
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`API Gateway running on port ${PORT}`);
  console.log(`AI Service: ${AI_SERVICE_URL}`);
  console.log(`Product Service: ${PRODUCT_SERVICE_URL}`);
  console.log(`User Service: ${USER_SERVICE_URL}`);
  console.log(`Order Service: ${ORDER_SERVICE_URL}`);
});

module.exports = app;
